{"ast": null, "code": "/**\n * @mui/private-theming v6.4.6\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport { default as ThemeProvider } from \"./ThemeProvider/index.js\";\nexport * from \"./ThemeProvider/index.js\";\nexport { default as useTheme } from \"./useTheme/index.js\";", "map": {"version": 3, "names": ["default", "ThemeProvider", "useTheme"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/private-theming/index.js"], "sourcesContent": ["/**\n * @mui/private-theming v6.4.6\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport { default as ThemeProvider } from \"./ThemeProvider/index.js\";\nexport * from \"./ThemeProvider/index.js\";\nexport { default as useTheme } from \"./useTheme/index.js\";"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,aAAa,QAAQ,0BAA0B;AACnE,cAAc,0BAA0B;AACxC,SAASD,OAAO,IAAIE,QAAQ,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}