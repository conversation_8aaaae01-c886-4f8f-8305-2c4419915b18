{"ast": null, "code": "export { default } from \"./spacing.js\";\nexport * from \"./spacing.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/system/esm/spacing/index.js"], "sourcesContent": ["export { default } from \"./spacing.js\";\nexport * from \"./spacing.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}