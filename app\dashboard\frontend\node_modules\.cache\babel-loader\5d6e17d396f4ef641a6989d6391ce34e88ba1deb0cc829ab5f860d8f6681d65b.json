{"ast": null, "code": "export { default } from \"./ownerWindow.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/utils/esm/ownerWindow/index.js"], "sourcesContent": ["export { default } from \"./ownerWindow.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}