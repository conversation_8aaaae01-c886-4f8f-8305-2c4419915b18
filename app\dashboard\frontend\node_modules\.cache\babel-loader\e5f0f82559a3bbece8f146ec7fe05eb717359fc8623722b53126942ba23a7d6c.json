{"ast": null, "code": "'use client';\n\n/* eslint-disable no-constant-condition */\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nfunction stripDiacritics(string) {\n  return string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n}\nexport function createFilterOptions() {\n  let config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, _ref) => {\n    let {\n      inputValue,\n      getOptionLabel\n    } = _ref;\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.parentElement?.contains(document.activeElement);\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction getInputValue(value, multiple, getOptionLabel) {\n  if (multiple || value == null) {\n    return '';\n  }\n  const optionLabel = getOptionLabel(value);\n  return typeof optionLabel === 'string' ? optionLabel : '';\n}\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => option.label ?? option,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n\n  // Calculate the initial inputValue on mount only.\n  // Using useRef since defaultValue doesn't need to update inputValue dynamically.\n  const initialInputValue = React.useRef(getInputValue(defaultValue, multiple, getOptionLabel)).current;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: initialInputValue,\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue, reason) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    const newInputValue = getInputValue(newValue, multiple, getOptionLabel);\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, reason);\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value, 'reset');\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(_ref2 => {\n    let {\n      event,\n      index,\n      reason = 'auto'\n    } = _ref2;\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(_ref3 => {\n    let {\n      event,\n      diff,\n      direction = 'next',\n      reason = 'auto'\n    } = _ref3;\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return filteredOptions.findIndex(option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && value.findIndex(val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = filteredOptions.findIndex(optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = function (event, option) {\n    let reasonProp = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'selectOption';\n    let origin = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'options';\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = newValue.findIndex(valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue, reason);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value, 'blur');\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: function () {\n      let other = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return {\n        ...other,\n        onKeyDown: handleKeyDown(other),\n        onMouseDown: handleMouseDown,\n        onClick: handleClick\n      };\n    },\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: _ref4 => {\n      let {\n        index\n      } = _ref4;\n      return {\n        key: index,\n        'data-tag-index': index,\n        tabIndex: -1,\n        ...(!readOnly && {\n          onDelete: handleTagDelete(index)\n        })\n      };\n    },\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: _ref5 => {\n      let {\n        index,\n        option\n      } = _ref5;\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionKey?.(option) ?? getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;", "map": {"version": 3, "names": ["React", "unstable_setRef", "setRef", "unstable_useEventCallback", "useEventCallback", "unstable_useControlled", "useControlled", "unstable_useId", "useId", "usePreviousProps", "stripDiacritics", "string", "normalize", "replace", "createFilterOptions", "config", "arguments", "length", "undefined", "ignoreAccents", "ignoreCase", "limit", "matchFrom", "stringify", "trim", "options", "_ref", "inputValue", "getOptionLabel", "input", "toLowerCase", "filteredOptions", "filter", "option", "candidate", "startsWith", "includes", "slice", "defaultFilterOptions", "pageSize", "defaultIsActiveElementInListbox", "listboxRef", "current", "parentElement", "contains", "document", "activeElement", "MULTIPLE_DEFAULT_VALUE", "getInputValue", "value", "multiple", "optionLabel", "useAutocomplete", "props", "unstable_isActiveElementInListbox", "unstable_classNamePrefix", "autoComplete", "autoHighlight", "autoSelect", "blurOnSelect", "clearOnBlur", "freeSolo", "clearOnEscape", "componentName", "defaultValue", "disableClearable", "disableCloseOnSelect", "disabled", "disabledProp", "disabledItemsFocusable", "disableListWrap", "filterOptions", "filterSelectedOptions", "getOptionDisabled", "getOption<PERSON>ey", "getOptionLabelProp", "label", "groupBy", "handleHomeEndKeys", "id", "idProp", "includeInputInList", "inputValueProp", "isOptionEqualToValue", "onChange", "onClose", "onHighlightChange", "onInputChange", "onOpen", "open", "openProp", "openOnFocus", "readOnly", "selectOnFocus", "valueProp", "process", "env", "NODE_ENV", "erroneousReturn", "console", "error", "JSON", "String", "ignoreFocus", "useRef", "firstFocus", "inputRef", "anchorEl", "setAnchorEl", "useState", "focusedTag", "setFocusedTag", "defaultHighlighted", "highlightedIndexRef", "initialInputValue", "setValueState", "controlled", "default", "name", "setInputValueState", "state", "focused", "setFocused", "resetInputValue", "useCallback", "event", "newValue", "reason", "isOptionSelected", "newInputValue", "setOpenState", "inputPristine", "setInputPristine", "inputValueIsSelectedValue", "popupOpen", "some", "value2", "previousProps", "useEffect", "valueChange", "listboxAvailable", "focusTag", "tagToFocus", "focus", "querySelector", "validOptionIndex", "index", "direction", "nextFocus", "nextFocusDisabled", "getAttribute", "hasAttribute", "setHighlightedIndex", "_ref2", "removeAttribute", "setAttribute", "prev", "classList", "remove", "listboxNode", "scrollTop", "add", "scrollHeight", "clientHeight", "element", "scrollBottom", "elementBottom", "offsetTop", "offsetHeight", "changeHighlightedIndex", "_ref3", "diff", "getNextIndex", "maxIndex", "newIndex", "Math", "abs", "nextIndex", "indexOf", "setSelectionRange", "getPreviousHighlightedOptionIndex", "isSameValue", "value1", "label1", "label2", "every", "val", "i", "previousHighlightedOption", "findIndex", "syncHighlightedIndex", "previousHighlightedOptionIndex", "valueItem", "currentOption", "itemIndex", "optionItem", "handleListboxRef", "node", "nodeName", "warn", "join", "handleOpen", "handleClose", "handleValue", "details", "is<PERSON><PERSON>ch", "selectNewValue", "reasonProp", "origin", "Array", "isArray", "matches", "push", "splice", "ctrl<PERSON>ey", "metaKey", "blur", "validTagIndex", "handleFocusTag", "nextTag", "handleClear", "handleKeyDown", "other", "onKeyDown", "defaultMuiPrevented", "key", "which", "preventDefault", "stopPropagation", "handleFocus", "handleBlur", "handleInputChange", "target", "handleOptionMouseMove", "Number", "currentTarget", "handleOptionTouchStart", "handleOptionClick", "handleTagDelete", "handlePopupIndicator", "handleMouseDown", "handleClick", "selectionEnd", "selectionStart", "select", "handleInputMouseDown", "dirty", "groupedOptions", "indexBy", "Map", "reduce", "acc", "group", "get", "set", "getRootProps", "onMouseDown", "onClick", "getInputLabelProps", "htmlFor", "getInputProps", "onBlur", "onFocus", "ref", "autoCapitalize", "spell<PERSON>heck", "role", "getClearProps", "tabIndex", "type", "getPopupIndicatorProps", "getTagProps", "_ref4", "onDelete", "getListboxProps", "getOptionProps", "_ref5", "selected", "onMouseMove", "onTouchStart", "expanded"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/useAutocomplete/useAutocomplete.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable no-constant-condition */\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nfunction stripDiacritics(string) {\n  return string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.parentElement?.contains(document.activeElement);\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction getInputValue(value, multiple, getOptionLabel) {\n  if (multiple || value == null) {\n    return '';\n  }\n  const optionLabel = getOptionLabel(value);\n  return typeof optionLabel === 'string' ? optionLabel : '';\n}\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => option.label ?? option,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n\n  // Calculate the initial inputValue on mount only.\n  // Using useRef since defaultValue doesn't need to update inputValue dynamically.\n  const initialInputValue = React.useRef(getInputValue(defaultValue, multiple, getOptionLabel)).current;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: initialInputValue,\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue, reason) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    const newInputValue = getInputValue(newValue, multiple, getOptionLabel);\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, reason);\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value, 'reset');\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason = 'auto'\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason = 'auto'\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return filteredOptions.findIndex(option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && value.findIndex(val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = filteredOptions.findIndex(optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = newValue.findIndex(valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue, reason);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value, 'blur');\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => ({\n      ...other,\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => ({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: handleTagDelete(index)\n      })\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionKey?.(option) ?? getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,IAAIC,MAAM,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,cAAc,IAAIC,KAAK,EAAEC,gBAAgB,QAAQ,YAAY;;AAEzL;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAOA,MAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;AAChE;AACA,OAAO,SAASC,mBAAmBA,CAAA,EAAc;EAAA,IAAbC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC7C,MAAM;IACJG,aAAa,GAAG,IAAI;IACpBC,UAAU,GAAG,IAAI;IACjBC,KAAK;IACLC,SAAS,GAAG,KAAK;IACjBC,SAAS;IACTC,IAAI,GAAG;EACT,CAAC,GAAGT,MAAM;EACV,OAAO,CAACU,OAAO,EAAAC,IAAA,KAGT;IAAA,IAHW;MACfC,UAAU;MACVC;IACF,CAAC,GAAAF,IAAA;IACC,IAAIG,KAAK,GAAGL,IAAI,GAAGG,UAAU,CAACH,IAAI,CAAC,CAAC,GAAGG,UAAU;IACjD,IAAIP,UAAU,EAAE;MACdS,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;IAC7B;IACA,IAAIX,aAAa,EAAE;MACjBU,KAAK,GAAGnB,eAAe,CAACmB,KAAK,CAAC;IAChC;IACA,MAAME,eAAe,GAAG,CAACF,KAAK,GAAGJ,OAAO,GAAGA,OAAO,CAACO,MAAM,CAACC,MAAM,IAAI;MAClE,IAAIC,SAAS,GAAG,CAACX,SAAS,IAAIK,cAAc,EAAEK,MAAM,CAAC;MACrD,IAAIb,UAAU,EAAE;QACdc,SAAS,GAAGA,SAAS,CAACJ,WAAW,CAAC,CAAC;MACrC;MACA,IAAIX,aAAa,EAAE;QACjBe,SAAS,GAAGxB,eAAe,CAACwB,SAAS,CAAC;MACxC;MACA,OAAOZ,SAAS,KAAK,OAAO,GAAGY,SAAS,CAACC,UAAU,CAACN,KAAK,CAAC,GAAGK,SAAS,CAACE,QAAQ,CAACP,KAAK,CAAC;IACxF,CAAC,CAAC;IACF,OAAO,OAAOR,KAAK,KAAK,QAAQ,GAAGU,eAAe,CAACM,KAAK,CAAC,CAAC,EAAEhB,KAAK,CAAC,GAAGU,eAAe;EACtF,CAAC;AACH;AACA,MAAMO,oBAAoB,GAAGxB,mBAAmB,CAAC,CAAC;;AAElD;AACA,MAAMyB,QAAQ,GAAG,CAAC;AAClB,MAAMC,+BAA+B,GAAGC,UAAU,IAAIA,UAAU,CAACC,OAAO,KAAK,IAAI,IAAID,UAAU,CAACC,OAAO,CAACC,aAAa,EAAEC,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC;AACvJ,MAAMC,sBAAsB,GAAG,EAAE;AACjC,SAASC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAEtB,cAAc,EAAE;EACtD,IAAIsB,QAAQ,IAAID,KAAK,IAAI,IAAI,EAAE;IAC7B,OAAO,EAAE;EACX;EACA,MAAME,WAAW,GAAGvB,cAAc,CAACqB,KAAK,CAAC;EACzC,OAAO,OAAOE,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG,EAAE;AAC3D;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,MAAM;IACJ;IACAC,iCAAiC,GAAGd,+BAA+B;IACnE;IACAe,wBAAwB,GAAG,KAAK;IAChCC,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAG,KAAK;IAClBC,YAAY,GAAG,KAAK;IACpBC,WAAW,GAAG,CAACP,KAAK,CAACQ,QAAQ;IAC7BC,aAAa,GAAG,KAAK;IACrBC,aAAa,GAAG,iBAAiB;IACjCC,YAAY,GAAGX,KAAK,CAACH,QAAQ,GAAGH,sBAAsB,GAAG,IAAI;IAC7DkB,gBAAgB,GAAG,KAAK;IACxBC,oBAAoB,GAAG,KAAK;IAC5BC,QAAQ,EAAEC,YAAY;IACtBC,sBAAsB,GAAG,KAAK;IAC9BC,eAAe,GAAG,KAAK;IACvBC,aAAa,GAAGjC,oBAAoB;IACpCkC,qBAAqB,GAAG,KAAK;IAC7BX,QAAQ,GAAG,KAAK;IAChBY,iBAAiB;IACjBC,YAAY;IACZ9C,cAAc,EAAE+C,kBAAkB,GAAG1C,MAAM,IAAIA,MAAM,CAAC2C,KAAK,IAAI3C,MAAM;IACrE4C,OAAO;IACPC,iBAAiB,GAAG,CAACzB,KAAK,CAACQ,QAAQ;IACnCkB,EAAE,EAAEC,MAAM;IACVC,kBAAkB,GAAG,KAAK;IAC1BtD,UAAU,EAAEuD,cAAc;IAC1BC,oBAAoB,GAAGA,CAAClD,MAAM,EAAEgB,KAAK,KAAKhB,MAAM,KAAKgB,KAAK;IAC1DC,QAAQ,GAAG,KAAK;IAChBkC,QAAQ;IACRC,OAAO;IACPC,iBAAiB;IACjBC,aAAa;IACbC,MAAM;IACNC,IAAI,EAAEC,QAAQ;IACdC,WAAW,GAAG,KAAK;IACnBlE,OAAO;IACPmE,QAAQ,GAAG,KAAK;IAChBC,aAAa,GAAG,CAACxC,KAAK,CAACQ,QAAQ;IAC/BZ,KAAK,EAAE6C;EACT,CAAC,GAAGzC,KAAK;EACT,MAAM0B,EAAE,GAAGvE,KAAK,CAACwE,MAAM,CAAC;EACxB,IAAIpD,cAAc,GAAG+C,kBAAkB;EACvC/C,cAAc,GAAGK,MAAM,IAAI;IACzB,MAAMkB,WAAW,GAAGwB,kBAAkB,CAAC1C,MAAM,CAAC;IAC9C,IAAI,OAAOkB,WAAW,KAAK,QAAQ,EAAE;MACnC,IAAI4C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMC,eAAe,GAAG/C,WAAW,KAAKjC,SAAS,GAAG,WAAW,GAAG,GAAG,OAAOiC,WAAW,KAAKA,WAAW,GAAG;QAC1GgD,OAAO,CAACC,KAAK,CAAC,yCAAyCrC,aAAa,aAAamC,eAAe,4BAA4BG,IAAI,CAAC9E,SAAS,CAACU,MAAM,CAAC,GAAG,CAAC;MACxJ;MACA,OAAOqE,MAAM,CAACnD,WAAW,CAAC;IAC5B;IACA,OAAOA,WAAW;EACpB,CAAC;EACD,MAAMoD,WAAW,GAAGvG,KAAK,CAACwG,MAAM,CAAC,KAAK,CAAC;EACvC,MAAMC,UAAU,GAAGzG,KAAK,CAACwG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG1G,KAAK,CAACwG,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM/D,UAAU,GAAGzC,KAAK,CAACwG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG5G,KAAK,CAAC6G,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/G,KAAK,CAAC6G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAMG,kBAAkB,GAAGvD,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,MAAMwD,mBAAmB,GAAGjH,KAAK,CAACwG,MAAM,CAACQ,kBAAkB,CAAC;;EAE5D;EACA;EACA,MAAME,iBAAiB,GAAGlH,KAAK,CAACwG,MAAM,CAACxD,aAAa,CAACgB,YAAY,EAAEd,QAAQ,EAAEtB,cAAc,CAAC,CAAC,CAACc,OAAO;EACrG,MAAM,CAACO,KAAK,EAAEkE,aAAa,CAAC,GAAG7G,aAAa,CAAC;IAC3C8G,UAAU,EAAEtB,SAAS;IACrBuB,OAAO,EAAErD,YAAY;IACrBsD,IAAI,EAAEvD;EACR,CAAC,CAAC;EACF,MAAM,CAACpC,UAAU,EAAE4F,kBAAkB,CAAC,GAAGjH,aAAa,CAAC;IACrD8G,UAAU,EAAElC,cAAc;IAC1BmC,OAAO,EAAEH,iBAAiB;IAC1BI,IAAI,EAAEvD,aAAa;IACnByD,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1H,KAAK,CAAC6G,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMc,eAAe,GAAG3H,KAAK,CAAC4H,WAAW,CAAC,CAACC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,KAAK;IACrE;IACA;IACA,MAAMC,gBAAgB,GAAG9E,QAAQ,GAAGD,KAAK,CAAChC,MAAM,GAAG6G,QAAQ,CAAC7G,MAAM,GAAG6G,QAAQ,KAAK,IAAI;IACtF,IAAI,CAACE,gBAAgB,IAAI,CAACpE,WAAW,EAAE;MACrC;IACF;IACA,MAAMqE,aAAa,GAAGjF,aAAa,CAAC8E,QAAQ,EAAE5E,QAAQ,EAAEtB,cAAc,CAAC;IACvE,IAAID,UAAU,KAAKsG,aAAa,EAAE;MAChC;IACF;IACAV,kBAAkB,CAACU,aAAa,CAAC;IACjC,IAAI1C,aAAa,EAAE;MACjBA,aAAa,CAACsC,KAAK,EAAEI,aAAa,EAAEF,MAAM,CAAC;IAC7C;EACF,CAAC,EAAE,CAACnG,cAAc,EAAED,UAAU,EAAEuB,QAAQ,EAAEqC,aAAa,EAAEgC,kBAAkB,EAAE3D,WAAW,EAAEX,KAAK,CAAC,CAAC;EACjG,MAAM,CAACwC,IAAI,EAAEyC,YAAY,CAAC,GAAG5H,aAAa,CAAC;IACzC8G,UAAU,EAAE1B,QAAQ;IACpB2B,OAAO,EAAE,KAAK;IACdC,IAAI,EAAEvD,aAAa;IACnByD,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGpI,KAAK,CAAC6G,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMwB,yBAAyB,GAAG,CAACnF,QAAQ,IAAID,KAAK,IAAI,IAAI,IAAItB,UAAU,KAAKC,cAAc,CAACqB,KAAK,CAAC;EACpG,MAAMqF,SAAS,GAAG7C,IAAI,IAAI,CAACG,QAAQ;EACnC,MAAM7D,eAAe,GAAGuG,SAAS,GAAG/D,aAAa,CAAC9C,OAAO,CAACO,MAAM,CAACC,MAAM,IAAI;IACzE,IAAIuC,qBAAqB,IAAI,CAACtB,QAAQ,GAAGD,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEsF,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,IAAI,IAAIrD,oBAAoB,CAAClD,MAAM,EAAEuG,MAAM,CAAC,CAAC,EAAE;MACjI,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;EACA;EACA;IACE7G,UAAU,EAAE0G,yBAAyB,IAAIF,aAAa,GAAG,EAAE,GAAGxG,UAAU;IACxEC;EACF,CAAC,CAAC,GAAG,EAAE;EACP,MAAM6G,aAAa,GAAGhI,gBAAgB,CAAC;IACrCsB,eAAe;IACfkB,KAAK;IACLtB;EACF,CAAC,CAAC;EACF3B,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAG1F,KAAK,KAAKwF,aAAa,CAACxF,KAAK;IACjD,IAAIwE,OAAO,IAAI,CAACkB,WAAW,EAAE;MAC3B;IACF;;IAEA;IACA,IAAI9E,QAAQ,IAAI,CAAC8E,WAAW,EAAE;MAC5B;IACF;IACAhB,eAAe,CAAC,IAAI,EAAE1E,KAAK,EAAE,OAAO,CAAC;EACvC,CAAC,EAAE,CAACA,KAAK,EAAE0E,eAAe,EAAEF,OAAO,EAAEgB,aAAa,CAACxF,KAAK,EAAEY,QAAQ,CAAC,CAAC;EACpE,MAAM+E,gBAAgB,GAAGnD,IAAI,IAAI1D,eAAe,CAACd,MAAM,GAAG,CAAC,IAAI,CAAC2E,QAAQ;EACxE,MAAMiD,QAAQ,GAAGzI,gBAAgB,CAAC0I,UAAU,IAAI;IAC9C,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBpC,QAAQ,CAAChE,OAAO,CAACqG,KAAK,CAAC,CAAC;IAC1B,CAAC,MAAM;MACLpC,QAAQ,CAACqC,aAAa,CAAC,oBAAoBF,UAAU,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;IACpE;EACF,CAAC,CAAC;;EAEF;EACA/I,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpB,IAAIxF,QAAQ,IAAI4D,UAAU,GAAG7D,KAAK,CAAChC,MAAM,GAAG,CAAC,EAAE;MAC7C8F,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB8B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC5F,KAAK,EAAEC,QAAQ,EAAE4D,UAAU,EAAE+B,QAAQ,CAAC,CAAC;EAC3C,SAASI,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;IAC1C,IAAI,CAAC1G,UAAU,CAACC,OAAO,IAAIwG,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAInH,eAAe,CAACd,MAAM,EAAE;MACvE,OAAO,CAAC,CAAC;IACX;IACA,IAAImI,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX,MAAMjH,MAAM,GAAGQ,UAAU,CAACC,OAAO,CAACsG,aAAa,CAAC,uBAAuBI,SAAS,IAAI,CAAC;;MAErF;MACA,MAAMC,iBAAiB,GAAGhF,sBAAsB,GAAG,KAAK,GAAG,CAACpC,MAAM,IAAIA,MAAM,CAACkC,QAAQ,IAAIlC,MAAM,CAACqH,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;MACxI,IAAIrH,MAAM,IAAIA,MAAM,CAACsH,YAAY,CAAC,UAAU,CAAC,IAAI,CAACF,iBAAiB,EAAE;QACnE;QACA,OAAOD,SAAS;MAClB;;MAEA;MACA;MACA,IAAID,SAAS,KAAK,MAAM,EAAE;QACxBC,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,IAAIrH,eAAe,CAACd,MAAM;MACtD,CAAC,MAAM;QACLmI,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,GAAGrH,eAAe,CAACd,MAAM,IAAIc,eAAe,CAACd,MAAM;MAC/E;;MAEA;MACA;MACA,IAAImI,SAAS,KAAKF,KAAK,EAAE;QACvB,OAAO,CAAC,CAAC;MACX;IACF;EACF;EACA,MAAMM,mBAAmB,GAAGpJ,gBAAgB,CAACqJ,KAAA,IAIvC;IAAA,IAJwC;MAC5C5B,KAAK;MACLqB,KAAK;MACLnB,MAAM,GAAG;IACX,CAAC,GAAA0B,KAAA;IACCxC,mBAAmB,CAACvE,OAAO,GAAGwG,KAAK;;IAEnC;IACA,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBxC,QAAQ,CAAChE,OAAO,CAACgH,eAAe,CAAC,uBAAuB,CAAC;IAC3D,CAAC,MAAM;MACLhD,QAAQ,CAAChE,OAAO,CAACiH,YAAY,CAAC,uBAAuB,EAAE,GAAG5E,EAAE,WAAWmE,KAAK,EAAE,CAAC;IACjF;IACA,IAAI5D,iBAAiB,EAAE;MACrBA,iBAAiB,CAACuC,KAAK,EAAEqB,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGnH,eAAe,CAACmH,KAAK,CAAC,EAAEnB,MAAM,CAAC;IAChF;IACA,IAAI,CAACtF,UAAU,CAACC,OAAO,EAAE;MACvB;IACF;IACA,MAAMkH,IAAI,GAAGnH,UAAU,CAACC,OAAO,CAACsG,aAAa,CAAC,mBAAmBzF,wBAAwB,UAAU,CAAC;IACpG,IAAIqG,IAAI,EAAE;MACRA,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,GAAGvG,wBAAwB,UAAU,CAAC;MAC5DqG,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,GAAGvG,wBAAwB,eAAe,CAAC;IACnE;IACA,IAAIwG,WAAW,GAAGtH,UAAU,CAACC,OAAO;IACpC,IAAID,UAAU,CAACC,OAAO,CAAC4G,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;MACzDS,WAAW,GAAGtH,UAAU,CAACC,OAAO,CAACC,aAAa,CAACqG,aAAa,CAAC,kBAAkB,CAAC;IAClF;;IAEA;IACA,IAAI,CAACe,WAAW,EAAE;MAChB;IACF;IACA,IAAIb,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBa,WAAW,CAACC,SAAS,GAAG,CAAC;MACzB;IACF;IACA,MAAM/H,MAAM,GAAGQ,UAAU,CAACC,OAAO,CAACsG,aAAa,CAAC,uBAAuBE,KAAK,IAAI,CAAC;IACjF,IAAI,CAACjH,MAAM,EAAE;MACX;IACF;IACAA,MAAM,CAAC4H,SAAS,CAACI,GAAG,CAAC,GAAG1G,wBAAwB,UAAU,CAAC;IAC3D,IAAIwE,MAAM,KAAK,UAAU,EAAE;MACzB9F,MAAM,CAAC4H,SAAS,CAACI,GAAG,CAAC,GAAG1G,wBAAwB,eAAe,CAAC;IAClE;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAIwG,WAAW,CAACG,YAAY,GAAGH,WAAW,CAACI,YAAY,IAAIpC,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,OAAO,EAAE;MACnG,MAAMqC,OAAO,GAAGnI,MAAM;MACtB,MAAMoI,YAAY,GAAGN,WAAW,CAACI,YAAY,GAAGJ,WAAW,CAACC,SAAS;MACrE,MAAMM,aAAa,GAAGF,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY;MAC9D,IAAIF,aAAa,GAAGD,YAAY,EAAE;QAChCN,WAAW,CAACC,SAAS,GAAGM,aAAa,GAAGP,WAAW,CAACI,YAAY;MAClE,CAAC,MAAM,IAAIC,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAI3F,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGkF,WAAW,CAACC,SAAS,EAAE;QACjGD,WAAW,CAACC,SAAS,GAAGI,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAI3F,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC;MACxF;IACF;EACF,CAAC,CAAC;EACF,MAAM4F,sBAAsB,GAAGrK,gBAAgB,CAACsK,KAAA,IAK1C;IAAA,IAL2C;MAC/C7C,KAAK;MACL8C,IAAI;MACJxB,SAAS,GAAG,MAAM;MAClBpB,MAAM,GAAG;IACX,CAAC,GAAA2C,KAAA;IACC,IAAI,CAACpC,SAAS,EAAE;MACd;IACF;IACA,MAAMsC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,QAAQ,GAAG9I,eAAe,CAACd,MAAM,GAAG,CAAC;MAC3C,IAAI0J,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO3D,kBAAkB;MAC3B;MACA,IAAI2D,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,CAAC;MACV;MACA,IAAIA,IAAI,KAAK,KAAK,EAAE;QAClB,OAAOE,QAAQ;MACjB;MACA,MAAMC,QAAQ,GAAG7D,mBAAmB,CAACvE,OAAO,GAAGiI,IAAI;MACnD,IAAIG,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAI7F,kBAAkB,EAAE;UACzC,OAAO,CAAC,CAAC;QACX;QACA,IAAIX,eAAe,IAAI2C,mBAAmB,CAACvE,OAAO,KAAK,CAAC,CAAC,IAAIqI,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UAC/E,OAAO,CAAC;QACV;QACA,OAAOE,QAAQ;MACjB;MACA,IAAIC,QAAQ,GAAGD,QAAQ,EAAE;QACvB,IAAIC,QAAQ,KAAKD,QAAQ,GAAG,CAAC,IAAI5F,kBAAkB,EAAE;UACnD,OAAO,CAAC,CAAC;QACX;QACA,IAAIX,eAAe,IAAIyG,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UACzC,OAAOE,QAAQ;QACjB;QACA,OAAO,CAAC;MACV;MACA,OAAOC,QAAQ;IACjB,CAAC;IACD,MAAMG,SAAS,GAAGhC,gBAAgB,CAAC2B,YAAY,CAAC,CAAC,EAAEzB,SAAS,CAAC;IAC7DK,mBAAmB,CAAC;MAClBN,KAAK,EAAE+B,SAAS;MAChBlD,MAAM;MACNF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIrE,YAAY,IAAImH,IAAI,KAAK,OAAO,EAAE;MACpC,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBvE,QAAQ,CAAChE,OAAO,CAACO,KAAK,GAAGtB,UAAU;MACrC,CAAC,MAAM;QACL,MAAMM,MAAM,GAAGL,cAAc,CAACG,eAAe,CAACkJ,SAAS,CAAC,CAAC;QACzDvE,QAAQ,CAAChE,OAAO,CAACO,KAAK,GAAGhB,MAAM;;QAE/B;QACA;QACA,MAAMiH,KAAK,GAAGjH,MAAM,CAACH,WAAW,CAAC,CAAC,CAACoJ,OAAO,CAACvJ,UAAU,CAACG,WAAW,CAAC,CAAC,CAAC;QACpE,IAAIoH,KAAK,KAAK,CAAC,IAAIvH,UAAU,CAACV,MAAM,GAAG,CAAC,EAAE;UACxCyF,QAAQ,CAAChE,OAAO,CAACyI,iBAAiB,CAACxJ,UAAU,CAACV,MAAM,EAAEgB,MAAM,CAAChB,MAAM,CAAC;QACtE;MACF;IACF;EACF,CAAC,CAAC;EACF,MAAMmK,iCAAiC,GAAGA,CAAA,KAAM;IAC9C,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAE9C,MAAM,KAAK;MACtC,MAAM+C,MAAM,GAAGD,MAAM,GAAG1J,cAAc,CAAC0J,MAAM,CAAC,GAAG,EAAE;MACnD,MAAME,MAAM,GAAGhD,MAAM,GAAG5G,cAAc,CAAC4G,MAAM,CAAC,GAAG,EAAE;MACnD,OAAO+C,MAAM,KAAKC,MAAM;IAC1B,CAAC;IACD,IAAIvE,mBAAmB,CAACvE,OAAO,KAAK,CAAC,CAAC,IAAI+F,aAAa,CAAC1G,eAAe,IAAI0G,aAAa,CAAC1G,eAAe,CAACd,MAAM,KAAKc,eAAe,CAACd,MAAM,IAAIwH,aAAa,CAAC9G,UAAU,KAAKA,UAAU,KAAKuB,QAAQ,GAAGD,KAAK,CAAChC,MAAM,KAAKwH,aAAa,CAACxF,KAAK,CAAChC,MAAM,IAAIwH,aAAa,CAACxF,KAAK,CAACwI,KAAK,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAK/J,cAAc,CAACqB,KAAK,CAAC0I,CAAC,CAAC,CAAC,KAAK/J,cAAc,CAAC8J,GAAG,CAAC,CAAC,GAAGL,WAAW,CAAC5C,aAAa,CAACxF,KAAK,EAAEA,KAAK,CAAC,CAAC,EAAE;MACtX,MAAM2I,yBAAyB,GAAGnD,aAAa,CAAC1G,eAAe,CAACkF,mBAAmB,CAACvE,OAAO,CAAC;MAC5F,IAAIkJ,yBAAyB,EAAE;QAC7B,OAAO7J,eAAe,CAAC8J,SAAS,CAAC5J,MAAM,IAAI;UACzC,OAAOL,cAAc,CAACK,MAAM,CAAC,KAAKL,cAAc,CAACgK,yBAAyB,CAAC;QAC7E,CAAC,CAAC;MACJ;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,MAAME,oBAAoB,GAAG9L,KAAK,CAAC4H,WAAW,CAAC,MAAM;IACnD,IAAI,CAACU,SAAS,EAAE;MACd;IACF;;IAEA;IACA;IACA,MAAMyD,8BAA8B,GAAGX,iCAAiC,CAAC,CAAC;IAC1E,IAAIW,8BAA8B,KAAK,CAAC,CAAC,EAAE;MACzC9E,mBAAmB,CAACvE,OAAO,GAAGqJ,8BAA8B;MAC5D;IACF;IACA,MAAMC,SAAS,GAAG9I,QAAQ,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;;IAE7C;IACA,IAAIlB,eAAe,CAACd,MAAM,KAAK,CAAC,IAAI+K,SAAS,IAAI,IAAI,EAAE;MACrDvB,sBAAsB,CAAC;QACrBE,IAAI,EAAE;MACR,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAAClI,UAAU,CAACC,OAAO,EAAE;MACvB;IACF;;IAEA;IACA,IAAIsJ,SAAS,IAAI,IAAI,EAAE;MACrB,MAAMC,aAAa,GAAGlK,eAAe,CAACkF,mBAAmB,CAACvE,OAAO,CAAC;;MAElE;MACA,IAAIQ,QAAQ,IAAI+I,aAAa,IAAIhJ,KAAK,CAAC4I,SAAS,CAACH,GAAG,IAAIvG,oBAAoB,CAAC8G,aAAa,EAAEP,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACxG;MACF;MACA,MAAMQ,SAAS,GAAGnK,eAAe,CAAC8J,SAAS,CAACM,UAAU,IAAIhH,oBAAoB,CAACgH,UAAU,EAAEH,SAAS,CAAC,CAAC;MACtG,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBzB,sBAAsB,CAAC;UACrBE,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnB,mBAAmB,CAAC;UAClBN,KAAK,EAAEgD;QACT,CAAC,CAAC;MACJ;MACA;IACF;;IAEA;IACA,IAAIjF,mBAAmB,CAACvE,OAAO,IAAIX,eAAe,CAACd,MAAM,GAAG,CAAC,EAAE;MAC7DuI,mBAAmB,CAAC;QAClBN,KAAK,EAAEnH,eAAe,CAACd,MAAM,GAAG;MAClC,CAAC,CAAC;MACF;IACF;;IAEA;IACAuI,mBAAmB,CAAC;MAClBN,KAAK,EAAEjC,mBAAmB,CAACvE;IAC7B,CAAC,CAAC;IACF;IACA;EACF,CAAC,EAAE;EACH;EACAX,eAAe,CAACd,MAAM;EACtB;EACA;EACAiC,QAAQ,GAAG,KAAK,GAAGD,KAAK,EAAEuB,qBAAqB,EAAEiG,sBAAsB,EAAEjB,mBAAmB,EAAElB,SAAS,EAAE3G,UAAU,EAAEuB,QAAQ,CAAC,CAAC;EAC/H,MAAMkJ,gBAAgB,GAAGhM,gBAAgB,CAACiM,IAAI,IAAI;IAChDnM,MAAM,CAACuC,UAAU,EAAE4J,IAAI,CAAC;IACxB,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACAP,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,IAAI/F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAjG,KAAK,CAAC0I,SAAS,CAAC,MAAM;MACpB,IAAI,CAAChC,QAAQ,CAAChE,OAAO,IAAIgE,QAAQ,CAAChE,OAAO,CAAC4J,QAAQ,KAAK,OAAO,EAAE;QAC9D,IAAI5F,QAAQ,CAAChE,OAAO,IAAIgE,QAAQ,CAAChE,OAAO,CAAC4J,QAAQ,KAAK,UAAU,EAAE;UAChEnG,OAAO,CAACoG,IAAI,CAAC,CAAC,sCAAsCxI,aAAa,4BAA4B,EAAE,4EAA4E,EAAE,4GAA4G,EAAE,mFAAmF,CAAC,CAACyI,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7X,CAAC,MAAM;UACLrG,OAAO,CAACC,KAAK,CAAC,CAAC,6DAA6DM,QAAQ,CAAChE,OAAO,0CAA0C,EAAE,YAAYqB,aAAa,4BAA4B,EAAE,EAAE,EAAEA,aAAa,KAAK,iBAAiB,GAAG,kHAAkH,GAAG,8DAA8D,CAAC,CAACyI,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3a;MACF;IACF,CAAC,EAAE,CAACzI,aAAa,CAAC,CAAC;EACrB;EACA/D,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpBoD,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAC1B,MAAMW,UAAU,GAAG5E,KAAK,IAAI;IAC1B,IAAIpC,IAAI,EAAE;MACR;IACF;IACAyC,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI5C,MAAM,EAAE;MACVA,MAAM,CAACqC,KAAK,CAAC;IACf;EACF,CAAC;EACD,MAAM6E,WAAW,GAAGA,CAAC7E,KAAK,EAAEE,MAAM,KAAK;IACrC,IAAI,CAACtC,IAAI,EAAE;MACT;IACF;IACAyC,YAAY,CAAC,KAAK,CAAC;IACnB,IAAI7C,OAAO,EAAE;MACXA,OAAO,CAACwC,KAAK,EAAEE,MAAM,CAAC;IACxB;EACF,CAAC;EACD,MAAM4E,WAAW,GAAGA,CAAC9E,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE6E,OAAO,KAAK;IACxD,IAAI1J,QAAQ,EAAE;MACZ,IAAID,KAAK,CAAChC,MAAM,KAAK6G,QAAQ,CAAC7G,MAAM,IAAIgC,KAAK,CAACwI,KAAK,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,KAAK5D,QAAQ,CAAC6D,CAAC,CAAC,CAAC,EAAE;QACpF;MACF;IACF,CAAC,MAAM,IAAI1I,KAAK,KAAK6E,QAAQ,EAAE;MAC7B;IACF;IACA,IAAI1C,QAAQ,EAAE;MACZA,QAAQ,CAACyC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE6E,OAAO,CAAC;IAC5C;IACAzF,aAAa,CAACW,QAAQ,CAAC;EACzB,CAAC;EACD,MAAM+E,OAAO,GAAG7M,KAAK,CAACwG,MAAM,CAAC,KAAK,CAAC;EACnC,MAAMsG,cAAc,GAAG,SAAAA,CAACjF,KAAK,EAAE5F,MAAM,EAAsD;IAAA,IAApD8K,UAAU,GAAA/L,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,cAAc;IAAA,IAAEgM,MAAM,GAAAhM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;IACpF,IAAI+G,MAAM,GAAGgF,UAAU;IACvB,IAAIjF,QAAQ,GAAG7F,MAAM;IACrB,IAAIiB,QAAQ,EAAE;MACZ4E,QAAQ,GAAGmF,KAAK,CAACC,OAAO,CAACjK,KAAK,CAAC,GAAGA,KAAK,CAACZ,KAAK,CAAC,CAAC,GAAG,EAAE;MACpD,IAAI0D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMkH,OAAO,GAAGrF,QAAQ,CAAC9F,MAAM,CAAC0J,GAAG,IAAIvG,oBAAoB,CAAClD,MAAM,EAAEyJ,GAAG,CAAC,CAAC;QACzE,IAAIyB,OAAO,CAAClM,MAAM,GAAG,CAAC,EAAE;UACtBkF,OAAO,CAACC,KAAK,CAAC,CAAC,+CAA+CrC,aAAa,2CAA2C,EAAE,0EAA0EoJ,OAAO,CAAClM,MAAM,WAAW,CAAC,CAACuL,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1O;MACF;MACA,MAAMN,SAAS,GAAGpE,QAAQ,CAAC+D,SAAS,CAACG,SAAS,IAAI7G,oBAAoB,CAAClD,MAAM,EAAE+J,SAAS,CAAC,CAAC;MAC1F,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBpE,QAAQ,CAACsF,IAAI,CAACnL,MAAM,CAAC;MACvB,CAAC,MAAM,IAAI+K,MAAM,KAAK,UAAU,EAAE;QAChClF,QAAQ,CAACuF,MAAM,CAACnB,SAAS,EAAE,CAAC,CAAC;QAC7BnE,MAAM,GAAG,cAAc;MACzB;IACF;IACAJ,eAAe,CAACE,KAAK,EAAEC,QAAQ,EAAEC,MAAM,CAAC;IACxC4E,WAAW,CAAC9E,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE;MACnC9F;IACF,CAAC,CAAC;IACF,IAAI,CAACiC,oBAAoB,KAAK,CAAC2D,KAAK,IAAI,CAACA,KAAK,CAACyF,OAAO,IAAI,CAACzF,KAAK,CAAC0F,OAAO,CAAC,EAAE;MACzEb,WAAW,CAAC7E,KAAK,EAAEE,MAAM,CAAC;IAC5B;IACA,IAAIpE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,OAAO,IAAIkJ,OAAO,CAACnK,OAAO,IAAIiB,YAAY,KAAK,OAAO,IAAI,CAACkJ,OAAO,CAACnK,OAAO,EAAE;MACxHgE,QAAQ,CAAChE,OAAO,CAAC8K,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EACD,SAASC,aAAaA,CAACvE,KAAK,EAAEC,SAAS,EAAE;IACvC,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IACA,IAAIE,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX;MACA,IAAIC,SAAS,KAAK,MAAM,IAAIC,SAAS,KAAKnG,KAAK,CAAChC,MAAM,IAAIkI,SAAS,KAAK,UAAU,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QACtG,OAAO,CAAC,CAAC;MACX;MACA,MAAMnH,MAAM,GAAG0E,QAAQ,CAACqC,aAAa,CAAC,oBAAoBI,SAAS,IAAI,CAAC;;MAExE;MACA,IAAI,CAACnH,MAAM,IAAI,CAACA,MAAM,CAACsH,YAAY,CAAC,UAAU,CAAC,IAAItH,MAAM,CAACkC,QAAQ,IAAIlC,MAAM,CAACqH,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;QACrHF,SAAS,IAAID,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOC,SAAS;MAClB;IACF;EACF;EACA,MAAMsE,cAAc,GAAGA,CAAC7F,KAAK,EAAEsB,SAAS,KAAK;IAC3C,IAAI,CAACjG,QAAQ,EAAE;MACb;IACF;IACA,IAAIvB,UAAU,KAAK,EAAE,EAAE;MACrB+K,WAAW,CAAC7E,KAAK,EAAE,aAAa,CAAC;IACnC;IACA,IAAI8F,OAAO,GAAG7G,UAAU;IACxB,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,IAAInF,UAAU,KAAK,EAAE,IAAIwH,SAAS,KAAK,UAAU,EAAE;QACjDwE,OAAO,GAAG1K,KAAK,CAAChC,MAAM,GAAG,CAAC;MAC5B;IACF,CAAC,MAAM;MACL0M,OAAO,IAAIxE,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACxC,IAAIwE,OAAO,GAAG,CAAC,EAAE;QACfA,OAAO,GAAG,CAAC;MACb;MACA,IAAIA,OAAO,KAAK1K,KAAK,CAAChC,MAAM,EAAE;QAC5B0M,OAAO,GAAG,CAAC,CAAC;MACd;IACF;IACAA,OAAO,GAAGF,aAAa,CAACE,OAAO,EAAExE,SAAS,CAAC;IAC3CpC,aAAa,CAAC4G,OAAO,CAAC;IACtB9E,QAAQ,CAAC8E,OAAO,CAAC;EACnB,CAAC;EACD,MAAMC,WAAW,GAAG/F,KAAK,IAAI;IAC3BtB,WAAW,CAAC7D,OAAO,GAAG,IAAI;IAC1B6E,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAIhC,aAAa,EAAE;MACjBA,aAAa,CAACsC,KAAK,EAAE,EAAE,EAAE,OAAO,CAAC;IACnC;IACA8E,WAAW,CAAC9E,KAAK,EAAE3E,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,CAAC;EACnD,CAAC;EACD,MAAM2K,aAAa,GAAGC,KAAK,IAAIjG,KAAK,IAAI;IACtC,IAAIiG,KAAK,CAACC,SAAS,EAAE;MACnBD,KAAK,CAACC,SAAS,CAAClG,KAAK,CAAC;IACxB;IACA,IAAIA,KAAK,CAACmG,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAIlH,UAAU,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC1E,QAAQ,CAACyF,KAAK,CAACoG,GAAG,CAAC,EAAE;MACzElH,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB8B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA;IACA,IAAIhB,KAAK,CAACqG,KAAK,KAAK,GAAG,EAAE;MACvB,QAAQrG,KAAK,CAACoG,GAAG;QACf,KAAK,MAAM;UACT,IAAI3F,SAAS,IAAIxD,iBAAiB,EAAE;YAClC;YACA+C,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtB1D,sBAAsB,CAAC;cACrBE,IAAI,EAAE,OAAO;cACbxB,SAAS,EAAE,MAAM;cACjBpB,MAAM,EAAE,UAAU;cAClBF;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,KAAK;UACR,IAAIS,SAAS,IAAIxD,iBAAiB,EAAE;YAClC;YACA+C,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtB1D,sBAAsB,CAAC;cACrBE,IAAI,EAAE,KAAK;cACXxB,SAAS,EAAE,UAAU;cACrBpB,MAAM,EAAE,UAAU;cAClBF;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,QAAQ;UACX;UACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;UACtB1D,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAACpI,QAAQ;YACf4G,SAAS,EAAE,UAAU;YACrBpB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF4E,UAAU,CAAC5E,KAAK,CAAC;UACjB;QACF,KAAK,UAAU;UACb;UACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;UACtB1D,sBAAsB,CAAC;YACrBE,IAAI,EAAEpI,QAAQ;YACd4G,SAAS,EAAE,MAAM;YACjBpB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF4E,UAAU,CAAC5E,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd;UACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;UACtB1D,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAAC;YACPxB,SAAS,EAAE,MAAM;YACjBpB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF4E,UAAU,CAAC5E,KAAK,CAAC;UACjB;QACF,KAAK,SAAS;UACZ;UACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;UACtB1D,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAAC,CAAC;YACRxB,SAAS,EAAE,UAAU;YACrBpB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF4E,UAAU,CAAC5E,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd6F,cAAc,CAAC7F,KAAK,EAAE,UAAU,CAAC;UACjC;QACF,KAAK,YAAY;UACf6F,cAAc,CAAC7F,KAAK,EAAE,MAAM,CAAC;UAC7B;QACF,KAAK,OAAO;UACV,IAAIZ,mBAAmB,CAACvE,OAAO,KAAK,CAAC,CAAC,IAAI4F,SAAS,EAAE;YACnD,MAAMrG,MAAM,GAAGF,eAAe,CAACkF,mBAAmB,CAACvE,OAAO,CAAC;YAC3D,MAAMyB,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAACxC,MAAM,CAAC,GAAG,KAAK;;YAEtE;YACA4F,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtB,IAAIhK,QAAQ,EAAE;cACZ;YACF;YACA2I,cAAc,CAACjF,KAAK,EAAE5F,MAAM,EAAE,cAAc,CAAC;;YAE7C;YACA,IAAIuB,YAAY,EAAE;cAChBkD,QAAQ,CAAChE,OAAO,CAACyI,iBAAiB,CAACzE,QAAQ,CAAChE,OAAO,CAACO,KAAK,CAAChC,MAAM,EAAEyF,QAAQ,CAAChE,OAAO,CAACO,KAAK,CAAChC,MAAM,CAAC;YAClG;UACF,CAAC,MAAM,IAAI4C,QAAQ,IAAIlC,UAAU,KAAK,EAAE,IAAI0G,yBAAyB,KAAK,KAAK,EAAE;YAC/E,IAAInF,QAAQ,EAAE;cACZ;cACA2E,KAAK,CAACsG,cAAc,CAAC,CAAC;YACxB;YACArB,cAAc,CAACjF,KAAK,EAAElG,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;UAC/D;UACA;QACF,KAAK,QAAQ;UACX,IAAI2G,SAAS,EAAE;YACb;YACAT,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtB;YACAtG,KAAK,CAACuG,eAAe,CAAC,CAAC;YACvB1B,WAAW,CAAC7E,KAAK,EAAE,QAAQ,CAAC;UAC9B,CAAC,MAAM,IAAI/D,aAAa,KAAKnC,UAAU,KAAK,EAAE,IAAIuB,QAAQ,IAAID,KAAK,CAAChC,MAAM,GAAG,CAAC,CAAC,EAAE;YAC/E;YACA4G,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtB;YACAtG,KAAK,CAACuG,eAAe,CAAC,CAAC;YACvBR,WAAW,CAAC/F,KAAK,CAAC;UACpB;UACA;QACF,KAAK,WAAW;UACd;UACA,IAAI3E,QAAQ,IAAI,CAAC0C,QAAQ,IAAIjE,UAAU,KAAK,EAAE,IAAIsB,KAAK,CAAChC,MAAM,GAAG,CAAC,EAAE;YAClE,MAAMiI,KAAK,GAAGpC,UAAU,KAAK,CAAC,CAAC,GAAG7D,KAAK,CAAChC,MAAM,GAAG,CAAC,GAAG6F,UAAU;YAC/D,MAAMgB,QAAQ,GAAG7E,KAAK,CAACZ,KAAK,CAAC,CAAC;YAC9ByF,QAAQ,CAACuF,MAAM,CAACnE,KAAK,EAAE,CAAC,CAAC;YACzByD,WAAW,CAAC9E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3C7F,MAAM,EAAEgB,KAAK,CAACiG,KAAK;YACrB,CAAC,CAAC;UACJ;UACA;QACF,KAAK,QAAQ;UACX;UACA,IAAIhG,QAAQ,IAAI,CAAC0C,QAAQ,IAAIjE,UAAU,KAAK,EAAE,IAAIsB,KAAK,CAAChC,MAAM,GAAG,CAAC,IAAI6F,UAAU,KAAK,CAAC,CAAC,EAAE;YACvF,MAAMoC,KAAK,GAAGpC,UAAU;YACxB,MAAMgB,QAAQ,GAAG7E,KAAK,CAACZ,KAAK,CAAC,CAAC;YAC9ByF,QAAQ,CAACuF,MAAM,CAACnE,KAAK,EAAE,CAAC,CAAC;YACzByD,WAAW,CAAC9E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3C7F,MAAM,EAAEgB,KAAK,CAACiG,KAAK;YACrB,CAAC,CAAC;UACJ;UACA;QACF;MACF;IACF;EACF,CAAC;EACD,MAAMmF,WAAW,GAAGxG,KAAK,IAAI;IAC3BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI/B,WAAW,IAAI,CAACY,WAAW,CAAC7D,OAAO,EAAE;MACvC+J,UAAU,CAAC5E,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMyG,UAAU,GAAGzG,KAAK,IAAI;IAC1B;IACA,IAAIvE,iCAAiC,CAACb,UAAU,CAAC,EAAE;MACjDiE,QAAQ,CAAChE,OAAO,CAACqG,KAAK,CAAC,CAAC;MACxB;IACF;IACArB,UAAU,CAAC,KAAK,CAAC;IACjBjB,UAAU,CAAC/D,OAAO,GAAG,IAAI;IACzB6D,WAAW,CAAC7D,OAAO,GAAG,KAAK;IAC3B,IAAIgB,UAAU,IAAIuD,mBAAmB,CAACvE,OAAO,KAAK,CAAC,CAAC,IAAI4F,SAAS,EAAE;MACjEwE,cAAc,CAACjF,KAAK,EAAE9F,eAAe,CAACkF,mBAAmB,CAACvE,OAAO,CAAC,EAAE,MAAM,CAAC;IAC7E,CAAC,MAAM,IAAIgB,UAAU,IAAIG,QAAQ,IAAIlC,UAAU,KAAK,EAAE,EAAE;MACtDmL,cAAc,CAACjF,KAAK,EAAElG,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;IACvD,CAAC,MAAM,IAAIiC,WAAW,EAAE;MACtB+D,eAAe,CAACE,KAAK,EAAE5E,KAAK,EAAE,MAAM,CAAC;IACvC;IACAyJ,WAAW,CAAC7E,KAAK,EAAE,MAAM,CAAC;EAC5B,CAAC;EACD,MAAM0G,iBAAiB,GAAG1G,KAAK,IAAI;IACjC,MAAMC,QAAQ,GAAGD,KAAK,CAAC2G,MAAM,CAACvL,KAAK;IACnC,IAAItB,UAAU,KAAKmG,QAAQ,EAAE;MAC3BP,kBAAkB,CAACO,QAAQ,CAAC;MAC5BM,gBAAgB,CAAC,KAAK,CAAC;MACvB,IAAI7C,aAAa,EAAE;QACjBA,aAAa,CAACsC,KAAK,EAAEC,QAAQ,EAAE,OAAO,CAAC;MACzC;IACF;IACA,IAAIA,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAC7D,gBAAgB,IAAI,CAACf,QAAQ,EAAE;QAClCyJ,WAAW,CAAC9E,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;MACnC;IACF,CAAC,MAAM;MACL4E,UAAU,CAAC5E,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAM4G,qBAAqB,GAAG5G,KAAK,IAAI;IACrC,MAAMqB,KAAK,GAAGwF,MAAM,CAAC7G,KAAK,CAAC8G,aAAa,CAACrF,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3E,IAAIrC,mBAAmB,CAACvE,OAAO,KAAKwG,KAAK,EAAE;MACzCM,mBAAmB,CAAC;QAClB3B,KAAK;QACLqB,KAAK;QACLnB,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM6G,sBAAsB,GAAG/G,KAAK,IAAI;IACtC2B,mBAAmB,CAAC;MAClB3B,KAAK;MACLqB,KAAK,EAAEwF,MAAM,CAAC7G,KAAK,CAAC8G,aAAa,CAACrF,YAAY,CAAC,mBAAmB,CAAC,CAAC;MACpEvB,MAAM,EAAE;IACV,CAAC,CAAC;IACF8E,OAAO,CAACnK,OAAO,GAAG,IAAI;EACxB,CAAC;EACD,MAAMmM,iBAAiB,GAAGhH,KAAK,IAAI;IACjC,MAAMqB,KAAK,GAAGwF,MAAM,CAAC7G,KAAK,CAAC8G,aAAa,CAACrF,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3EwD,cAAc,CAACjF,KAAK,EAAE9F,eAAe,CAACmH,KAAK,CAAC,EAAE,cAAc,CAAC;IAC7D2D,OAAO,CAACnK,OAAO,GAAG,KAAK;EACzB,CAAC;EACD,MAAMoM,eAAe,GAAG5F,KAAK,IAAIrB,KAAK,IAAI;IACxC,MAAMC,QAAQ,GAAG7E,KAAK,CAACZ,KAAK,CAAC,CAAC;IAC9ByF,QAAQ,CAACuF,MAAM,CAACnE,KAAK,EAAE,CAAC,CAAC;IACzByD,WAAW,CAAC9E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;MAC3C7F,MAAM,EAAEgB,KAAK,CAACiG,KAAK;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM6F,oBAAoB,GAAGlH,KAAK,IAAI;IACpC,IAAIpC,IAAI,EAAE;MACRiH,WAAW,CAAC7E,KAAK,EAAE,aAAa,CAAC;IACnC,CAAC,MAAM;MACL4E,UAAU,CAAC5E,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmH,eAAe,GAAGnH,KAAK,IAAI;IAC/B;IACA,IAAI,CAACA,KAAK,CAAC8G,aAAa,CAAC/L,QAAQ,CAACiF,KAAK,CAAC2G,MAAM,CAAC,EAAE;MAC/C;IACF;IACA,IAAI3G,KAAK,CAAC2G,MAAM,CAAClF,YAAY,CAAC,IAAI,CAAC,KAAKvE,EAAE,EAAE;MAC1C8C,KAAK,CAACsG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMc,WAAW,GAAGpH,KAAK,IAAI;IAC3B;IACA,IAAI,CAACA,KAAK,CAAC8G,aAAa,CAAC/L,QAAQ,CAACiF,KAAK,CAAC2G,MAAM,CAAC,EAAE;MAC/C;IACF;IACA9H,QAAQ,CAAChE,OAAO,CAACqG,KAAK,CAAC,CAAC;IACxB,IAAIlD,aAAa,IAAIY,UAAU,CAAC/D,OAAO,IAAIgE,QAAQ,CAAChE,OAAO,CAACwM,YAAY,GAAGxI,QAAQ,CAAChE,OAAO,CAACyM,cAAc,KAAK,CAAC,EAAE;MAChHzI,QAAQ,CAAChE,OAAO,CAAC0M,MAAM,CAAC,CAAC;IAC3B;IACA3I,UAAU,CAAC/D,OAAO,GAAG,KAAK;EAC5B,CAAC;EACD,MAAM2M,oBAAoB,GAAGxH,KAAK,IAAI;IACpC,IAAI,CAACzD,YAAY,KAAKzC,UAAU,KAAK,EAAE,IAAI,CAAC8D,IAAI,CAAC,EAAE;MACjDsJ,oBAAoB,CAAClH,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,IAAIyH,KAAK,GAAGzL,QAAQ,IAAIlC,UAAU,CAACV,MAAM,GAAG,CAAC;EAC7CqO,KAAK,GAAGA,KAAK,KAAKpM,QAAQ,GAAGD,KAAK,CAAChC,MAAM,GAAG,CAAC,GAAGgC,KAAK,KAAK,IAAI,CAAC;EAC/D,IAAIsM,cAAc,GAAGxN,eAAe;EACpC,IAAI8C,OAAO,EAAE;IACX;IACA,MAAM2K,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAIlD,IAAI,GAAG,KAAK;IAChBgD,cAAc,GAAGxN,eAAe,CAAC2N,MAAM,CAAC,CAACC,GAAG,EAAE1N,MAAM,EAAEiH,KAAK,KAAK;MAC9D,MAAM0G,KAAK,GAAG/K,OAAO,CAAC5C,MAAM,CAAC;MAC7B,IAAI0N,GAAG,CAAC1O,MAAM,GAAG,CAAC,IAAI0O,GAAG,CAACA,GAAG,CAAC1O,MAAM,GAAG,CAAC,CAAC,CAAC2O,KAAK,KAAKA,KAAK,EAAE;QACzDD,GAAG,CAACA,GAAG,CAAC1O,MAAM,GAAG,CAAC,CAAC,CAACQ,OAAO,CAAC2L,IAAI,CAACnL,MAAM,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI8D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIuJ,OAAO,CAACK,GAAG,CAACD,KAAK,CAAC,IAAI,CAACrD,IAAI,EAAE;YAC/BpG,OAAO,CAACoG,IAAI,CAAC,qEAAqExI,aAAa,8BAA8B,EAAE,8EAA8E,CAAC;YAC9MwI,IAAI,GAAG,IAAI;UACb;UACAiD,OAAO,CAACM,GAAG,CAACF,KAAK,EAAE,IAAI,CAAC;QAC1B;QACAD,GAAG,CAACvC,IAAI,CAAC;UACPa,GAAG,EAAE/E,KAAK;UACVA,KAAK;UACL0G,KAAK;UACLnO,OAAO,EAAE,CAACQ,MAAM;QAClB,CAAC,CAAC;MACJ;MACA,OAAO0N,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EACA,IAAIvL,YAAY,IAAIqD,OAAO,EAAE;IAC3B6G,UAAU,CAAC,CAAC;EACd;EACA,OAAO;IACLyB,YAAY,EAAE,SAAAA,CAAA;MAAA,IAACjC,KAAK,GAAA9M,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OAAM;QAC7B,GAAG8M,KAAK;QACRC,SAAS,EAAEF,aAAa,CAACC,KAAK,CAAC;QAC/BkC,WAAW,EAAEhB,eAAe;QAC5BiB,OAAO,EAAEhB;MACX,CAAC;IAAA,CAAC;IACFiB,kBAAkB,EAAEA,CAAA,MAAO;MACzBnL,EAAE,EAAE,GAAGA,EAAE,QAAQ;MACjBoL,OAAO,EAAEpL;IACX,CAAC,CAAC;IACFqL,aAAa,EAAEA,CAAA,MAAO;MACpBrL,EAAE;MACF9B,KAAK,EAAEtB,UAAU;MACjB0O,MAAM,EAAE/B,UAAU;MAClBgC,OAAO,EAAEjC,WAAW;MACpBjJ,QAAQ,EAAEmJ,iBAAiB;MAC3ByB,WAAW,EAAEX,oBAAoB;MACjC;MACA;MACA,uBAAuB,EAAE/G,SAAS,GAAG,EAAE,GAAG,IAAI;MAC9C,mBAAmB,EAAE9E,YAAY,GAAG,MAAM,GAAG,MAAM;MACnD,eAAe,EAAEoF,gBAAgB,GAAG,GAAG7D,EAAE,UAAU,GAAG7D,SAAS;MAC/D,eAAe,EAAE0H,gBAAgB;MACjC;MACA;MACApF,YAAY,EAAE,KAAK;MACnB+M,GAAG,EAAE7J,QAAQ;MACb8J,cAAc,EAAE,MAAM;MACtBC,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE,UAAU;MAChBvM,QAAQ,EAAEC;IACZ,CAAC,CAAC;IACFuM,aAAa,EAAEA,CAAA,MAAO;MACpBC,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAErC;IACX,CAAC,CAAC;IACFkD,sBAAsB,EAAEA,CAAA,MAAO;MAC7BF,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAElB;IACX,CAAC,CAAC;IACFgC,WAAW,EAAEC,KAAA;MAAA,IAAC;QACZ9H;MACF,CAAC,GAAA8H,KAAA;MAAA,OAAM;QACL/C,GAAG,EAAE/E,KAAK;QACV,gBAAgB,EAAEA,KAAK;QACvB0H,QAAQ,EAAE,CAAC,CAAC;QACZ,IAAI,CAAChL,QAAQ,IAAI;UACfqL,QAAQ,EAAEnC,eAAe,CAAC5F,KAAK;QACjC,CAAC;MACH,CAAC;IAAA,CAAC;IACFgI,eAAe,EAAEA,CAAA,MAAO;MACtBR,IAAI,EAAE,SAAS;MACf3L,EAAE,EAAE,GAAGA,EAAE,UAAU;MACnB,iBAAiB,EAAE,GAAGA,EAAE,QAAQ;MAChCwL,GAAG,EAAEnE,gBAAgB;MACrB4D,WAAW,EAAEnI,KAAK,IAAI;QACpB;QACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACFgD,cAAc,EAAEC,KAAA,IAGV;MAAA,IAHW;QACflI,KAAK;QACLjH;MACF,CAAC,GAAAmP,KAAA;MACC,MAAMC,QAAQ,GAAG,CAACnO,QAAQ,GAAGD,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEsF,IAAI,CAACC,MAAM,IAAIA,MAAM,IAAI,IAAI,IAAIrD,oBAAoB,CAAClD,MAAM,EAAEuG,MAAM,CAAC,CAAC;MACpH,MAAMrE,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAACxC,MAAM,CAAC,GAAG,KAAK;MACtE,OAAO;QACLgM,GAAG,EAAEvJ,YAAY,GAAGzC,MAAM,CAAC,IAAIL,cAAc,CAACK,MAAM,CAAC;QACrD2O,QAAQ,EAAE,CAAC,CAAC;QACZF,IAAI,EAAE,QAAQ;QACd3L,EAAE,EAAE,GAAGA,EAAE,WAAWmE,KAAK,EAAE;QAC3BoI,WAAW,EAAE7C,qBAAqB;QAClCwB,OAAO,EAAEpB,iBAAiB;QAC1B0C,YAAY,EAAE3C,sBAAsB;QACpC,mBAAmB,EAAE1F,KAAK;QAC1B,eAAe,EAAE/E,QAAQ;QACzB,eAAe,EAAEkN;MACnB,CAAC;IACH,CAAC;IACDtM,EAAE;IACFpD,UAAU;IACVsB,KAAK;IACLqM,KAAK;IACLkC,QAAQ,EAAElJ,SAAS,IAAI3B,QAAQ;IAC/B2B,SAAS;IACTb,OAAO,EAAEA,OAAO,IAAIX,UAAU,KAAK,CAAC,CAAC;IACrCH,QAAQ;IACRC,WAAW;IACXE,UAAU;IACVyI;EACF,CAAC;AACH;AACA,eAAenM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}