{"ast": null, "code": "var isDevelopment = false;\n\n/*\n\nBased off glamor's StyleSheet, thanks <PERSON><PERSON> ❤️\n\nhigh performance StyleSheet for css-in-js systems\n\n- uses multiple style tags behind the scenes for millions of rules\n- uses `insertRule` for appending in production for *much* faster performance\n\n// usage\n\nimport { StyleSheet } from '@emotion/sheet'\n\nlet styleSheet = new StyleSheet({ key: '', container: document.head })\n\nstyleSheet.insert('#box { border: 1px solid red; }')\n- appends a css rule into the stylesheet\n\nstyleSheet.flush()\n- empties the stylesheet of all its contents\n\n*/\n\nfunction sheetForTag(tag) {\n  if (tag.sheet) {\n    return tag.sheet;\n  } // this weirdness brought to you by firefox\n\n  /* istanbul ignore next */\n\n  for (var i = 0; i < document.styleSheets.length; i++) {\n    if (document.styleSheets[i].ownerNode === tag) {\n      return document.styleSheets[i];\n    }\n  } // this function should always return with a value\n  // TS can't understand it though so we make it stop complaining here\n\n  return undefined;\n}\nfunction createStyleElement(options) {\n  var tag = document.createElement('style');\n  tag.setAttribute('data-emotion', options.key);\n  if (options.nonce !== undefined) {\n    tag.setAttribute('nonce', options.nonce);\n  }\n  tag.appendChild(document.createTextNode(''));\n  tag.setAttribute('data-s', '');\n  return tag;\n}\nvar StyleSheet = /*#__PURE__*/function () {\n  // Using Node instead of HTMLElement since container may be a ShadowRoot\n  function StyleSheet(options) {\n    var _this = this;\n    this._insertTag = function (tag) {\n      var before;\n      if (_this.tags.length === 0) {\n        if (_this.insertionPoint) {\n          before = _this.insertionPoint.nextSibling;\n        } else if (_this.prepend) {\n          before = _this.container.firstChild;\n        } else {\n          before = _this.before;\n        }\n      } else {\n        before = _this.tags[_this.tags.length - 1].nextSibling;\n      }\n      _this.container.insertBefore(tag, before);\n      _this.tags.push(tag);\n    };\n    this.isSpeedy = options.speedy === undefined ? !isDevelopment : options.speedy;\n    this.tags = [];\n    this.ctr = 0;\n    this.nonce = options.nonce; // key is the value of the data-emotion attribute, it's used to identify different sheets\n\n    this.key = options.key;\n    this.container = options.container;\n    this.prepend = options.prepend;\n    this.insertionPoint = options.insertionPoint;\n    this.before = null;\n  }\n  var _proto = StyleSheet.prototype;\n  _proto.hydrate = function hydrate(nodes) {\n    nodes.forEach(this._insertTag);\n  };\n  _proto.insert = function insert(rule) {\n    // the max length is how many rules we have per style tag, it's 65000 in speedy mode\n    // it's 1 in dev because we insert source maps that map a single rule to a location\n    // and you can only have one source map per style tag\n    if (this.ctr % (this.isSpeedy ? 65000 : 1) === 0) {\n      this._insertTag(createStyleElement(this));\n    }\n    var tag = this.tags[this.tags.length - 1];\n    if (this.isSpeedy) {\n      var sheet = sheetForTag(tag);\n      try {\n        // this is the ultrafast version, works across browsers\n        // the big drawback is that the css won't be editable in devtools\n        sheet.insertRule(rule, sheet.cssRules.length);\n      } catch (e) {}\n    } else {\n      tag.appendChild(document.createTextNode(rule));\n    }\n    this.ctr++;\n  };\n  _proto.flush = function flush() {\n    this.tags.forEach(function (tag) {\n      var _tag$parentNode;\n      return (_tag$parentNode = tag.parentNode) == null ? void 0 : _tag$parentNode.removeChild(tag);\n    });\n    this.tags = [];\n    this.ctr = 0;\n  };\n  return StyleSheet;\n}();\nexport { StyleSheet };", "map": {"version": 3, "names": ["isDevelopment", "sheetForTag", "tag", "sheet", "i", "document", "styleSheets", "length", "ownerNode", "undefined", "createStyleElement", "options", "createElement", "setAttribute", "key", "nonce", "append<PERSON><PERSON><PERSON>", "createTextNode", "StyleSheet", "_this", "_insertTag", "before", "tags", "insertionPoint", "nextS<PERSON>ling", "prepend", "container", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "push", "isSpeedy", "speedy", "ctr", "_proto", "prototype", "hydrate", "nodes", "for<PERSON>ach", "insert", "rule", "insertRule", "cssRules", "e", "flush", "_tag$parentNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@emotion/sheet/dist/emotion-sheet.esm.js"], "sourcesContent": ["var isDevelopment = false;\n\n/*\n\nBased off glamor's StyleSheet, thanks <PERSON><PERSON> ❤️\n\nhigh performance StyleSheet for css-in-js systems\n\n- uses multiple style tags behind the scenes for millions of rules\n- uses `insertRule` for appending in production for *much* faster performance\n\n// usage\n\nimport { StyleSheet } from '@emotion/sheet'\n\nlet styleSheet = new StyleSheet({ key: '', container: document.head })\n\nstyleSheet.insert('#box { border: 1px solid red; }')\n- appends a css rule into the stylesheet\n\nstyleSheet.flush()\n- empties the stylesheet of all its contents\n\n*/\n\nfunction sheetForTag(tag) {\n  if (tag.sheet) {\n    return tag.sheet;\n  } // this weirdness brought to you by firefox\n\n  /* istanbul ignore next */\n\n\n  for (var i = 0; i < document.styleSheets.length; i++) {\n    if (document.styleSheets[i].ownerNode === tag) {\n      return document.styleSheets[i];\n    }\n  } // this function should always return with a value\n  // TS can't understand it though so we make it stop complaining here\n\n\n  return undefined;\n}\n\nfunction createStyleElement(options) {\n  var tag = document.createElement('style');\n  tag.setAttribute('data-emotion', options.key);\n\n  if (options.nonce !== undefined) {\n    tag.setAttribute('nonce', options.nonce);\n  }\n\n  tag.appendChild(document.createTextNode(''));\n  tag.setAttribute('data-s', '');\n  return tag;\n}\n\nvar StyleSheet = /*#__PURE__*/function () {\n  // Using Node instead of HTMLElement since container may be a ShadowRoot\n  function StyleSheet(options) {\n    var _this = this;\n\n    this._insertTag = function (tag) {\n      var before;\n\n      if (_this.tags.length === 0) {\n        if (_this.insertionPoint) {\n          before = _this.insertionPoint.nextSibling;\n        } else if (_this.prepend) {\n          before = _this.container.firstChild;\n        } else {\n          before = _this.before;\n        }\n      } else {\n        before = _this.tags[_this.tags.length - 1].nextSibling;\n      }\n\n      _this.container.insertBefore(tag, before);\n\n      _this.tags.push(tag);\n    };\n\n    this.isSpeedy = options.speedy === undefined ? !isDevelopment : options.speedy;\n    this.tags = [];\n    this.ctr = 0;\n    this.nonce = options.nonce; // key is the value of the data-emotion attribute, it's used to identify different sheets\n\n    this.key = options.key;\n    this.container = options.container;\n    this.prepend = options.prepend;\n    this.insertionPoint = options.insertionPoint;\n    this.before = null;\n  }\n\n  var _proto = StyleSheet.prototype;\n\n  _proto.hydrate = function hydrate(nodes) {\n    nodes.forEach(this._insertTag);\n  };\n\n  _proto.insert = function insert(rule) {\n    // the max length is how many rules we have per style tag, it's 65000 in speedy mode\n    // it's 1 in dev because we insert source maps that map a single rule to a location\n    // and you can only have one source map per style tag\n    if (this.ctr % (this.isSpeedy ? 65000 : 1) === 0) {\n      this._insertTag(createStyleElement(this));\n    }\n\n    var tag = this.tags[this.tags.length - 1];\n\n    if (this.isSpeedy) {\n      var sheet = sheetForTag(tag);\n\n      try {\n        // this is the ultrafast version, works across browsers\n        // the big drawback is that the css won't be editable in devtools\n        sheet.insertRule(rule, sheet.cssRules.length);\n      } catch (e) {\n      }\n    } else {\n      tag.appendChild(document.createTextNode(rule));\n    }\n\n    this.ctr++;\n  };\n\n  _proto.flush = function flush() {\n    this.tags.forEach(function (tag) {\n      var _tag$parentNode;\n\n      return (_tag$parentNode = tag.parentNode) == null ? void 0 : _tag$parentNode.removeChild(tag);\n    });\n    this.tags = [];\n    this.ctr = 0;\n  };\n\n  return StyleSheet;\n}();\n\nexport { StyleSheet };\n"], "mappings": "AAAA,IAAIA,aAAa,GAAG,KAAK;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,IAAIA,GAAG,CAACC,KAAK,EAAE;IACb,OAAOD,GAAG,CAACC,KAAK;EAClB,CAAC,CAAC;;EAEF;;EAGA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,QAAQ,CAACC,WAAW,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;IACpD,IAAIC,QAAQ,CAACC,WAAW,CAACF,CAAC,CAAC,CAACI,SAAS,KAAKN,GAAG,EAAE;MAC7C,OAAOG,QAAQ,CAACC,WAAW,CAACF,CAAC,CAAC;IAChC;EACF,CAAC,CAAC;EACF;;EAGA,OAAOK,SAAS;AAClB;AAEA,SAASC,kBAAkBA,CAACC,OAAO,EAAE;EACnC,IAAIT,GAAG,GAAGG,QAAQ,CAACO,aAAa,CAAC,OAAO,CAAC;EACzCV,GAAG,CAACW,YAAY,CAAC,cAAc,EAAEF,OAAO,CAACG,GAAG,CAAC;EAE7C,IAAIH,OAAO,CAACI,KAAK,KAAKN,SAAS,EAAE;IAC/BP,GAAG,CAACW,YAAY,CAAC,OAAO,EAAEF,OAAO,CAACI,KAAK,CAAC;EAC1C;EAEAb,GAAG,CAACc,WAAW,CAACX,QAAQ,CAACY,cAAc,CAAC,EAAE,CAAC,CAAC;EAC5Cf,GAAG,CAACW,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;EAC9B,OAAOX,GAAG;AACZ;AAEA,IAAIgB,UAAU,GAAG,aAAa,YAAY;EACxC;EACA,SAASA,UAAUA,CAACP,OAAO,EAAE;IAC3B,IAAIQ,KAAK,GAAG,IAAI;IAEhB,IAAI,CAACC,UAAU,GAAG,UAAUlB,GAAG,EAAE;MAC/B,IAAImB,MAAM;MAEV,IAAIF,KAAK,CAACG,IAAI,CAACf,MAAM,KAAK,CAAC,EAAE;QAC3B,IAAIY,KAAK,CAACI,cAAc,EAAE;UACxBF,MAAM,GAAGF,KAAK,CAACI,cAAc,CAACC,WAAW;QAC3C,CAAC,MAAM,IAAIL,KAAK,CAACM,OAAO,EAAE;UACxBJ,MAAM,GAAGF,KAAK,CAACO,SAAS,CAACC,UAAU;QACrC,CAAC,MAAM;UACLN,MAAM,GAAGF,KAAK,CAACE,MAAM;QACvB;MACF,CAAC,MAAM;QACLA,MAAM,GAAGF,KAAK,CAACG,IAAI,CAACH,KAAK,CAACG,IAAI,CAACf,MAAM,GAAG,CAAC,CAAC,CAACiB,WAAW;MACxD;MAEAL,KAAK,CAACO,SAAS,CAACE,YAAY,CAAC1B,GAAG,EAAEmB,MAAM,CAAC;MAEzCF,KAAK,CAACG,IAAI,CAACO,IAAI,CAAC3B,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAAC4B,QAAQ,GAAGnB,OAAO,CAACoB,MAAM,KAAKtB,SAAS,GAAG,CAACT,aAAa,GAAGW,OAAO,CAACoB,MAAM;IAC9E,IAAI,CAACT,IAAI,GAAG,EAAE;IACd,IAAI,CAACU,GAAG,GAAG,CAAC;IACZ,IAAI,CAACjB,KAAK,GAAGJ,OAAO,CAACI,KAAK,CAAC,CAAC;;IAE5B,IAAI,CAACD,GAAG,GAAGH,OAAO,CAACG,GAAG;IACtB,IAAI,CAACY,SAAS,GAAGf,OAAO,CAACe,SAAS;IAClC,IAAI,CAACD,OAAO,GAAGd,OAAO,CAACc,OAAO;IAC9B,IAAI,CAACF,cAAc,GAAGZ,OAAO,CAACY,cAAc;IAC5C,IAAI,CAACF,MAAM,GAAG,IAAI;EACpB;EAEA,IAAIY,MAAM,GAAGf,UAAU,CAACgB,SAAS;EAEjCD,MAAM,CAACE,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;IACvCA,KAAK,CAACC,OAAO,CAAC,IAAI,CAACjB,UAAU,CAAC;EAChC,CAAC;EAEDa,MAAM,CAACK,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;IACpC;IACA;IACA;IACA,IAAI,IAAI,CAACP,GAAG,IAAI,IAAI,CAACF,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MAChD,IAAI,CAACV,UAAU,CAACV,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC3C;IAEA,IAAIR,GAAG,GAAG,IAAI,CAACoB,IAAI,CAAC,IAAI,CAACA,IAAI,CAACf,MAAM,GAAG,CAAC,CAAC;IAEzC,IAAI,IAAI,CAACuB,QAAQ,EAAE;MACjB,IAAI3B,KAAK,GAAGF,WAAW,CAACC,GAAG,CAAC;MAE5B,IAAI;QACF;QACA;QACAC,KAAK,CAACqC,UAAU,CAACD,IAAI,EAAEpC,KAAK,CAACsC,QAAQ,CAAClC,MAAM,CAAC;MAC/C,CAAC,CAAC,OAAOmC,CAAC,EAAE,CACZ;IACF,CAAC,MAAM;MACLxC,GAAG,CAACc,WAAW,CAACX,QAAQ,CAACY,cAAc,CAACsB,IAAI,CAAC,CAAC;IAChD;IAEA,IAAI,CAACP,GAAG,EAAE;EACZ,CAAC;EAEDC,MAAM,CAACU,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,IAAI,CAACrB,IAAI,CAACe,OAAO,CAAC,UAAUnC,GAAG,EAAE;MAC/B,IAAI0C,eAAe;MAEnB,OAAO,CAACA,eAAe,GAAG1C,GAAG,CAAC2C,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,eAAe,CAACE,WAAW,CAAC5C,GAAG,CAAC;IAC/F,CAAC,CAAC;IACF,IAAI,CAACoB,IAAI,GAAG,EAAE;IACd,IAAI,CAACU,GAAG,GAAG,CAAC;EACd,CAAC;EAED,OAAOd,UAAU;AACnB,CAAC,CAAC,CAAC;AAEH,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}