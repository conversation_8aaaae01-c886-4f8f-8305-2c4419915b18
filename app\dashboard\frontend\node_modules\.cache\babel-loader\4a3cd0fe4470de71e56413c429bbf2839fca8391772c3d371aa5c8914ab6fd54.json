{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"\n}), 'ErrorOutline');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/internal/svg-icons/ErrorOutline.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"\n}), 'ErrorOutline');"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,8BAA8B;;AAExD;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}