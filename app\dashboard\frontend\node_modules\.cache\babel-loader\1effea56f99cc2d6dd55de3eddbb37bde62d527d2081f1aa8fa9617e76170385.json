{"ast": null, "code": "import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from \"./createMixins.js\";\nimport createPalette from \"./createPalette.js\";\nimport createTypography from \"./createTypography.js\";\nimport shadows from \"./shadows.js\";\nimport createTransitions from \"./createTransitions.js\";\nimport zIndex from \"./zIndex.js\";\nimport { stringifyTheme } from \"./stringifyTheme.js\";\nfunction createThemeNoVars() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    breakpoints: breakpointsInput,\n    mixins: mixinsInput = {},\n    spacing: spacingInput,\n    palette: paletteInput = {},\n    transitions: transitionsInput = {},\n    typography: typographyInput = {},\n    shape: shapeInput,\n    ...other\n  } = options;\n  if (options.vars &&\n  // The error should throw only for the root theme creation because user is not allowed to use a custom node `vars`.\n  // `generateThemeVars` is the closest identifier for checking that the `options` is a result of `createTheme` with CSS variables so that user can create new theme for nested ThemeProvider.\n  options.generateThemeVars === undefined) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `vars` is a private field used for CSS variables support.\\n' +\n    // #host-reference\n    'Please use another name or follow the [docs](https://mui.com/material-ui/customization/css-theme-variables/usage/) to enable the feature.' : _formatMuiErrorMessage(20));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: {\n      ...zIndex\n    }\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.includes(key) && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([`MUI: The \\`${component}\\` component increases ` + `the CSS specificity of the \\`${key}\\` internal state.`, 'You can not override it like this: ', JSON.stringify(node, null, 2), '', `Instead, you need to use the '&.${stateClass}' syntax:`, JSON.stringify({\n              root: {\n                [`&.${stateClass}`]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.startsWith('Mui')) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  muiTheme.toRuntimeSource = stringifyTheme; // for Pigment CSS integration\n\n  return muiTheme;\n}\nlet warnedOnce = false;\nexport function createMuiTheme() {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      warnedOnce = true;\n      console.error(['MUI: the createMuiTheme function was renamed to createTheme.', '', \"You should use `import { createTheme } from '@mui/material/styles'`\"].join('\\n'));\n    }\n  }\n  return createThemeNoVars(...arguments);\n}\nexport default createThemeNoVars;", "map": {"version": 3, "names": ["_formatMuiErrorMessage", "deepmerge", "styleFunctionSx", "unstable_defaultSxConfig", "defaultSxConfig", "systemCreateTheme", "generateUtilityClass", "createMixins", "createPalette", "createTypography", "shadows", "createTransitions", "zIndex", "stringifyTheme", "createThemeNoVars", "options", "arguments", "length", "undefined", "breakpoints", "breakpointsInput", "mixins", "mixinsInput", "spacing", "spacingInput", "palette", "paletteInput", "transitions", "transitionsInput", "typography", "typographyInput", "shape", "shapeInput", "other", "vars", "generateThemeVars", "Error", "process", "env", "NODE_ENV", "systemTheme", "muiTheme", "slice", "_len", "args", "Array", "_key", "reduce", "acc", "argument", "stateClasses", "traverse", "node", "component", "key", "child", "includes", "Object", "keys", "stateClass", "console", "error", "JSON", "stringify", "root", "join", "components", "for<PERSON>ach", "styleOverrides", "startsWith", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme", "toRuntimeSource", "warnedOnce", "createMuiTheme"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/styles/createThemeNoVars.js"], "sourcesContent": ["import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from \"./createMixins.js\";\nimport createPalette from \"./createPalette.js\";\nimport createTypography from \"./createTypography.js\";\nimport shadows from \"./shadows.js\";\nimport createTransitions from \"./createTransitions.js\";\nimport zIndex from \"./zIndex.js\";\nimport { stringifyTheme } from \"./stringifyTheme.js\";\nfunction createThemeNoVars(options = {}, ...args) {\n  const {\n    breakpoints: breakpointsInput,\n    mixins: mixinsInput = {},\n    spacing: spacingInput,\n    palette: paletteInput = {},\n    transitions: transitionsInput = {},\n    typography: typographyInput = {},\n    shape: shapeInput,\n    ...other\n  } = options;\n  if (options.vars &&\n  // The error should throw only for the root theme creation because user is not allowed to use a custom node `vars`.\n  // `generateThemeVars` is the closest identifier for checking that the `options` is a result of `createTheme` with CSS variables so that user can create new theme for nested ThemeProvider.\n  options.generateThemeVars === undefined) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `vars` is a private field used for CSS variables support.\\n' +\n    // #host-reference\n    'Please use another name or follow the [docs](https://mui.com/material-ui/customization/css-theme-variables/usage/) to enable the feature.' : _formatMuiErrorMessage(20));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: {\n      ...zIndex\n    }\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.includes(key) && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([`MUI: The \\`${component}\\` component increases ` + `the CSS specificity of the \\`${key}\\` internal state.`, 'You can not override it like this: ', JSON.stringify(node, null, 2), '', `Instead, you need to use the '&.${stateClass}' syntax:`, JSON.stringify({\n              root: {\n                [`&.${stateClass}`]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.startsWith('Mui')) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  muiTheme.toRuntimeSource = stringifyTheme; // for Pigment CSS integration\n\n  return muiTheme;\n}\nlet warnedOnce = false;\nexport function createMuiTheme(...args) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      warnedOnce = true;\n      console.error(['MUI: the createMuiTheme function was renamed to createTheme.', '', \"You should use `import { createTheme } from '@mui/material/styles'`\"].join('\\n'));\n    }\n  }\n  return createThemeNoVars(...args);\n}\nexport default createThemeNoVars;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,kCAAkC;AACrE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,eAAe,IAAIC,wBAAwB,IAAIC,eAAe,QAAQ,6BAA6B;AAC1G,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,iBAAiBA,CAAA,EAAwB;EAAA,IAAvBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACrC,MAAM;IACJG,WAAW,EAAEC,gBAAgB;IAC7BC,MAAM,EAAEC,WAAW,GAAG,CAAC,CAAC;IACxBC,OAAO,EAAEC,YAAY;IACrBC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;IAC1BC,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;IAClCC,UAAU,EAAEC,eAAe,GAAG,CAAC,CAAC;IAChCC,KAAK,EAAEC,UAAU;IACjB,GAAGC;EACL,CAAC,GAAGlB,OAAO;EACX,IAAIA,OAAO,CAACmB,IAAI;EAChB;EACA;EACAnB,OAAO,CAACoB,iBAAiB,KAAKjB,SAAS,EAAE;IACvC,MAAM,IAAIkB,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,kEAAkE;IAC1H;IACA,2IAA2I,GAAGvC,sBAAsB,CAAC,EAAE,CAAC,CAAC;EAC3K;EACA,MAAMyB,OAAO,GAAGjB,aAAa,CAACkB,YAAY,CAAC;EAC3C,MAAMc,WAAW,GAAGnC,iBAAiB,CAACU,OAAO,CAAC;EAC9C,IAAI0B,QAAQ,GAAGxC,SAAS,CAACuC,WAAW,EAAE;IACpCnB,MAAM,EAAEd,YAAY,CAACiC,WAAW,CAACrB,WAAW,EAAEG,WAAW,CAAC;IAC1DG,OAAO;IACP;IACAf,OAAO,EAAEA,OAAO,CAACgC,KAAK,CAAC,CAAC;IACxBb,UAAU,EAAEpB,gBAAgB,CAACgB,OAAO,EAAEK,eAAe,CAAC;IACtDH,WAAW,EAAEhB,iBAAiB,CAACiB,gBAAgB,CAAC;IAChDhB,MAAM,EAAE;MACN,GAAGA;IACL;EACF,CAAC,CAAC;EACF6B,QAAQ,GAAGxC,SAAS,CAACwC,QAAQ,EAAER,KAAK,CAAC;EAAC,SAAAU,IAAA,GAAA3B,SAAA,CAAAC,MAAA,EAhCI2B,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAA9B,SAAA,CAAA8B,IAAA;EAAA;EAiC9CL,QAAQ,GAAGG,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKhD,SAAS,CAAC+C,GAAG,EAAEC,QAAQ,CAAC,EAAER,QAAQ,CAAC;EAC7E,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAMW,YAAY,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3I,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;MACpC,IAAIC,GAAG;;MAEP;MACA,KAAKA,GAAG,IAAIF,IAAI,EAAE;QAChB,MAAMG,KAAK,GAAGH,IAAI,CAACE,GAAG,CAAC;QACvB,IAAIJ,YAAY,CAACM,QAAQ,CAACF,GAAG,CAAC,IAAIG,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACtC,MAAM,GAAG,CAAC,EAAE;UAC/D,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC,MAAMoB,UAAU,GAAGrD,oBAAoB,CAAC,EAAE,EAAEgD,GAAG,CAAC;YAChDM,OAAO,CAACC,KAAK,CAAC,CAAC,cAAcR,SAAS,yBAAyB,GAAG,gCAAgCC,GAAG,oBAAoB,EAAE,qCAAqC,EAAEQ,IAAI,CAACC,SAAS,CAACX,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,mCAAmCO,UAAU,WAAW,EAAEG,IAAI,CAACC,SAAS,CAAC;cAC5QC,IAAI,EAAE;gBACJ,CAAC,KAAKL,UAAU,EAAE,GAAGJ;cACvB;YACF,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,uCAAuC,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC;UACvE;UACA;UACAb,IAAI,CAACE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB;MACF;IACF,CAAC;IACDG,MAAM,CAACC,IAAI,CAACjB,QAAQ,CAACyB,UAAU,CAAC,CAACC,OAAO,CAACd,SAAS,IAAI;MACpD,MAAMe,cAAc,GAAG3B,QAAQ,CAACyB,UAAU,CAACb,SAAS,CAAC,CAACe,cAAc;MACpE,IAAIA,cAAc,IAAIf,SAAS,CAACgB,UAAU,CAAC,KAAK,CAAC,EAAE;QACjDlB,QAAQ,CAACiB,cAAc,EAAEf,SAAS,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;EACAZ,QAAQ,CAAC6B,iBAAiB,GAAG;IAC3B,GAAGlE,eAAe;IAClB,GAAG6B,KAAK,EAAEqC;EACZ,CAAC;EACD7B,QAAQ,CAAC8B,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAOvE,eAAe,CAAC;MACrBsE,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACDjC,QAAQ,CAACkC,eAAe,GAAG9D,cAAc,CAAC,CAAC;;EAE3C,OAAO4B,QAAQ;AACjB;AACA,IAAImC,UAAU,GAAG,KAAK;AACtB,OAAO,SAASC,cAAcA,CAAA,EAAU;EACtC,IAAIxC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACqC,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI;MACjBhB,OAAO,CAACC,KAAK,CAAC,CAAC,8DAA8D,EAAE,EAAE,EAAE,qEAAqE,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;IACvK;EACF;EACA,OAAOnD,iBAAiB,CAAC,GAAAE,SAAO,CAAC;AACnC;AACA,eAAeF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}