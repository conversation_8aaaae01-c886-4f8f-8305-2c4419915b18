{"ast": null, "code": "import generateUtilityClass from \"../generateUtilityClass/index.js\";\nexport default function generateUtilityClasses(componentName, slots) {\n  let globalStatePrefix = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'Mui';\n  const result = {};\n  slots.forEach(slot => {\n    result[slot] = generateUtilityClass(componentName, slot, globalStatePrefix);\n  });\n  return result;\n}", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "componentName", "slots", "globalStatePrefix", "arguments", "length", "undefined", "result", "for<PERSON>ach", "slot"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/utils/esm/generateUtilityClasses/generateUtilityClasses.js"], "sourcesContent": ["import generateUtilityClass from \"../generateUtilityClass/index.js\";\nexport default function generateUtilityClasses(componentName, slots, globalStatePrefix = 'Mui') {\n  const result = {};\n  slots.forEach(slot => {\n    result[slot] = generateUtilityClass(componentName, slot, globalStatePrefix);\n  });\n  return result;\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,kCAAkC;AACnE,eAAe,SAASC,sBAAsBA,CAACC,aAAa,EAAEC,KAAK,EAA6B;EAAA,IAA3BC,iBAAiB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC5F,MAAMG,MAAM,GAAG,CAAC,CAAC;EACjBL,KAAK,CAACM,OAAO,CAACC,IAAI,IAAI;IACpBF,MAAM,CAACE,IAAI,CAAC,GAAGV,oBAAoB,CAACE,aAAa,EAAEQ,IAAI,EAAEN,iBAAiB,CAAC;EAC7E,CAAC,CAAC;EACF,OAAOI,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}