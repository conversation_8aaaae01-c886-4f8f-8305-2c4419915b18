{"ast": null, "code": "// Configuration for the frontend application\n// Get API base URL from environment or use default\nexport const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:8000';// WebSocket URL\nexport const WS_URL=`${API_BASE_URL.replace('http','ws')}/ws`;// Other configuration settings\nexport const DEFAULT_REFRESH_INTERVAL=30000;// 30 seconds\nexport const PRICE_REFRESH_INTERVAL=5000;// 5 seconds", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "WS_URL", "replace", "DEFAULT_REFRESH_INTERVAL", "PRICE_REFRESH_INTERVAL"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/config.ts"], "sourcesContent": ["// Configuration for the frontend application\n\n// Get API base URL from environment or use default\nexport const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// WebSocket URL\nexport const WS_URL = `${API_BASE_URL.replace('http', 'ws')}/ws`;\n\n// Other configuration settings\nexport const DEFAULT_REFRESH_INTERVAL = 30000; // 30 seconds\nexport const PRICE_REFRESH_INTERVAL = 5000; // 5 seconds\n"], "mappings": "AAAA;AAEA;AACA,MAAO,MAAM,CAAAA,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAEpF;AACA,MAAO,MAAM,CAAAC,MAAM,CAAG,GAAGJ,YAAY,CAACK,OAAO,CAAC,MAAM,CAAE,IAAI,CAAC,KAAK,CAEhE;AACA,MAAO,MAAM,CAAAC,wBAAwB,CAAG,KAAK,CAAE;AAC/C,MAAO,MAAM,CAAAC,sBAAsB,CAAG,IAAI,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}