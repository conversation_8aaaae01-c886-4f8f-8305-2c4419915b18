{"ast": null, "code": "'use client';\n\n/* eslint-disable consistent-return, jsx-a11y/no-noninteractive-tabindex */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, elementAcceptingRef, unstable_useForkRef as useForkRef, unstable_ownerDocument as ownerDocument, unstable_getReactElementRef as getReactElementRef } from '@mui/utils';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n// Inspired by https://github.com/focus-trap/tabbable\nconst candidatesSelector = ['input', 'select', 'textarea', 'a[href]', 'button', '[tabindex]', 'audio[controls]', 'video[controls]', '[contenteditable]:not([contenteditable=\"false\"])'].join(',');\nfunction getTabIndex(node) {\n  const tabindexAttr = parseInt(node.getAttribute('tabindex') || '', 10);\n  if (!Number.isNaN(tabindexAttr)) {\n    return tabindexAttr;\n  }\n\n  // Browsers do not return `tabIndex` correctly for contentEditable nodes;\n  // https://issues.chromium.org/issues/41283952\n  // so if they don't have a tabindex attribute specifically set, assume it's 0.\n  // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n  //  `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n  //  yet they are still part of the regular tab order; in FF, they get a default\n  //  `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n  //  order, consider their tab index to be 0.\n  if (node.contentEditable === 'true' || (node.nodeName === 'AUDIO' || node.nodeName === 'VIDEO' || node.nodeName === 'DETAILS') && node.getAttribute('tabindex') === null) {\n    return 0;\n  }\n  return node.tabIndex;\n}\nfunction isNonTabbableRadio(node) {\n  if (node.tagName !== 'INPUT' || node.type !== 'radio') {\n    return false;\n  }\n  if (!node.name) {\n    return false;\n  }\n  const getRadio = selector => node.ownerDocument.querySelector(`input[type=\"radio\"]${selector}`);\n  let roving = getRadio(`[name=\"${node.name}\"]:checked`);\n  if (!roving) {\n    roving = getRadio(`[name=\"${node.name}\"]`);\n  }\n  return roving !== node;\n}\nfunction isNodeMatchingSelectorFocusable(node) {\n  if (node.disabled || node.tagName === 'INPUT' && node.type === 'hidden' || isNonTabbableRadio(node)) {\n    return false;\n  }\n  return true;\n}\nfunction defaultGetTabbable(root) {\n  const regularTabNodes = [];\n  const orderedTabNodes = [];\n  Array.from(root.querySelectorAll(candidatesSelector)).forEach((node, i) => {\n    const nodeTabIndex = getTabIndex(node);\n    if (nodeTabIndex === -1 || !isNodeMatchingSelectorFocusable(node)) {\n      return;\n    }\n    if (nodeTabIndex === 0) {\n      regularTabNodes.push(node);\n    } else {\n      orderedTabNodes.push({\n        documentOrder: i,\n        tabIndex: nodeTabIndex,\n        node: node\n      });\n    }\n  });\n  return orderedTabNodes.sort((a, b) => a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex).map(a => a.node).concat(regularTabNodes);\n}\nfunction defaultIsEnabled() {\n  return true;\n}\n\n/**\n * @ignore - internal component.\n */\nfunction FocusTrap(props) {\n  const {\n    children,\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableRestoreFocus = false,\n    getTabbable = defaultGetTabbable,\n    isEnabled = defaultIsEnabled,\n    open\n  } = props;\n  const ignoreNextEnforceFocus = React.useRef(false);\n  const sentinelStart = React.useRef(null);\n  const sentinelEnd = React.useRef(null);\n  const nodeToRestore = React.useRef(null);\n  const reactFocusEventTarget = React.useRef(null);\n  // This variable is useful when disableAutoFocus is true.\n  // It waits for the active element to move into the component to activate.\n  const activated = React.useRef(false);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(getReactElementRef(children), rootRef);\n  const lastKeydown = React.useRef(null);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    activated.current = !disableAutoFocus;\n  }, [disableAutoFocus, open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    if (!rootRef.current.contains(doc.activeElement)) {\n      if (!rootRef.current.hasAttribute('tabIndex')) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.error(['MUI: The modal content node does not accept focus.', 'For the benefit of assistive technologies, ' + 'the tabIndex of the node is being set to \"-1\".'].join('\\n'));\n        }\n        rootRef.current.setAttribute('tabIndex', '-1');\n      }\n      if (activated.current) {\n        rootRef.current.focus();\n      }\n    }\n    return () => {\n      // restoreLastFocus()\n      if (!disableRestoreFocus) {\n        // In IE11 it is possible for document.activeElement to be null resulting\n        // in nodeToRestore.current being null.\n        // Not all elements in IE11 have a focus method.\n        // Once IE11 support is dropped the focus() call can be unconditional.\n        if (nodeToRestore.current && nodeToRestore.current.focus) {\n          ignoreNextEnforceFocus.current = true;\n          nodeToRestore.current.focus();\n        }\n        nodeToRestore.current = null;\n      }\n    };\n    // Missing `disableRestoreFocus` which is fine.\n    // We don't support changing that prop on an open FocusTrap\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    const loopFocus = nativeEvent => {\n      lastKeydown.current = nativeEvent;\n      if (disableEnforceFocus || !isEnabled() || nativeEvent.key !== 'Tab') {\n        return;\n      }\n\n      // Make sure the next tab starts from the right place.\n      // doc.activeElement refers to the origin.\n      if (doc.activeElement === rootRef.current && nativeEvent.shiftKey) {\n        // We need to ignore the next contain as\n        // it will try to move the focus back to the rootRef element.\n        ignoreNextEnforceFocus.current = true;\n        if (sentinelEnd.current) {\n          sentinelEnd.current.focus();\n        }\n      }\n    };\n    const contain = () => {\n      const rootElement = rootRef.current;\n\n      // Cleanup functions are executed lazily in React 17.\n      // Contain can be called between the component being unmounted and its cleanup function being run.\n      if (rootElement === null) {\n        return;\n      }\n      if (!doc.hasFocus() || !isEnabled() || ignoreNextEnforceFocus.current) {\n        ignoreNextEnforceFocus.current = false;\n        return;\n      }\n\n      // The focus is already inside\n      if (rootElement.contains(doc.activeElement)) {\n        return;\n      }\n\n      // The disableEnforceFocus is set and the focus is outside of the focus trap (and sentinel nodes)\n      if (disableEnforceFocus && doc.activeElement !== sentinelStart.current && doc.activeElement !== sentinelEnd.current) {\n        return;\n      }\n\n      // if the focus event is not coming from inside the children's react tree, reset the refs\n      if (doc.activeElement !== reactFocusEventTarget.current) {\n        reactFocusEventTarget.current = null;\n      } else if (reactFocusEventTarget.current !== null) {\n        return;\n      }\n      if (!activated.current) {\n        return;\n      }\n      let tabbable = [];\n      if (doc.activeElement === sentinelStart.current || doc.activeElement === sentinelEnd.current) {\n        tabbable = getTabbable(rootRef.current);\n      }\n\n      // one of the sentinel nodes was focused, so move the focus\n      // to the first/last tabbable element inside the focus trap\n      if (tabbable.length > 0) {\n        const isShiftTab = Boolean(lastKeydown.current?.shiftKey && lastKeydown.current?.key === 'Tab');\n        const focusNext = tabbable[0];\n        const focusPrevious = tabbable[tabbable.length - 1];\n        if (typeof focusNext !== 'string' && typeof focusPrevious !== 'string') {\n          if (isShiftTab) {\n            focusPrevious.focus();\n          } else {\n            focusNext.focus();\n          }\n        }\n        // no tabbable elements in the trap focus or the focus was outside of the focus trap\n      } else {\n        rootElement.focus();\n      }\n    };\n    doc.addEventListener('focusin', contain);\n    doc.addEventListener('keydown', loopFocus, true);\n\n    // With Edge, Safari and Firefox, no focus related events are fired when the focused area stops being a focused area.\n    // for example https://bugzilla.mozilla.org/show_bug.cgi?id=559561.\n    // Instead, we can look if the active element was restored on the BODY element.\n    //\n    // The whatwg spec defines how the browser should behave but does not explicitly mention any events:\n    // https://html.spec.whatwg.org/multipage/interaction.html#focus-fixup-rule.\n    const interval = setInterval(() => {\n      if (doc.activeElement && doc.activeElement.tagName === 'BODY') {\n        contain();\n      }\n    }, 50);\n    return () => {\n      clearInterval(interval);\n      doc.removeEventListener('focusin', contain);\n      doc.removeEventListener('keydown', loopFocus, true);\n    };\n  }, [disableAutoFocus, disableEnforceFocus, disableRestoreFocus, isEnabled, open, getTabbable]);\n  const onFocus = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n    reactFocusEventTarget.current = event.target;\n    const childrenPropsHandler = children.props.onFocus;\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const handleFocusSentinel = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelStart,\n      \"data-testid\": \"sentinelStart\"\n    }), /*#__PURE__*/React.cloneElement(children, {\n      ref: handleRef,\n      onFocus\n    }), /*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelEnd,\n      \"data-testid\": \"sentinelEnd\"\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? FocusTrap.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef,\n  /**\n   * If `true`, the focus trap will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any focus trap children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the focus trap less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the focus trap will not prevent focus from leaving the focus trap while open.\n   *\n   * Generally this should never be set to `true` as it makes the focus trap less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, the focus trap will not restore focus to previously focused element once\n   * focus trap is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Returns an array of ordered tabbable nodes (i.e. in tab order) within the root.\n   * For instance, you can provide the \"tabbable\" npm dependency.\n   * @param {HTMLElement} root\n   */\n  getTabbable: PropTypes.func,\n  /**\n   * This prop extends the `open` prop.\n   * It allows to toggle the open state without having to wait for a rerender when changing the `open` prop.\n   * This prop should be memoized.\n   * It can be used to support multiple focus trap mounted at the same time.\n   * @default function defaultIsEnabled(): boolean {\n   *   return true;\n   * }\n   */\n  isEnabled: PropTypes.func,\n  /**\n   * If `true`, focus is locked.\n   */\n  open: PropTypes.bool.isRequired\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  FocusTrap['propTypes' + ''] = exactProp(FocusTrap.propTypes);\n}\nexport default FocusTrap;", "map": {"version": 3, "names": ["React", "PropTypes", "exactProp", "elementAcceptingRef", "unstable_useForkRef", "useForkRef", "unstable_ownerDocument", "ownerDocument", "unstable_getReactElementRef", "getReactElementRef", "jsx", "_jsx", "jsxs", "_jsxs", "candidatesSelector", "join", "getTabIndex", "node", "tabindexAttr", "parseInt", "getAttribute", "Number", "isNaN", "contentEditable", "nodeName", "tabIndex", "isNonTabbableRadio", "tagName", "type", "name", "getRadio", "selector", "querySelector", "roving", "isNodeMatchingSelectorFocusable", "disabled", "defaultGetTabbable", "root", "regularTabNodes", "orderedTabNodes", "Array", "from", "querySelectorAll", "for<PERSON>ach", "i", "nodeTabIndex", "push", "documentOrder", "sort", "a", "b", "map", "concat", "defaultIsEnabled", "FocusTrap", "props", "children", "disableAutoFocus", "disableEnforceFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTabbable", "isEnabled", "open", "ignoreNextEnforceFocus", "useRef", "sentinelStart", "sentinelEnd", "nodeToRestore", "reactFocusEventTarget", "activated", "rootRef", "handleRef", "lastKeydown", "useEffect", "current", "doc", "contains", "activeElement", "hasAttribute", "process", "env", "NODE_ENV", "console", "error", "setAttribute", "focus", "loopFocus", "nativeEvent", "key", "shift<PERSON>ey", "contain", "rootElement", "hasFocus", "tabbable", "length", "isShiftTab", "Boolean", "focusNext", "focusPrevious", "addEventListener", "interval", "setInterval", "clearInterval", "removeEventListener", "onFocus", "event", "relatedTarget", "target", "childrenPropsHandler", "handleFocusSentinel", "Fragment", "ref", "cloneElement", "propTypes", "bool", "func", "isRequired"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/Unstable_TrapFocus/FocusTrap.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable consistent-return, jsx-a11y/no-noninteractive-tabindex */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, elementAcceptingRef, unstable_useForkRef as useForkRef, unstable_ownerDocument as ownerDocument, unstable_getReactElementRef as getReactElementRef } from '@mui/utils';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n// Inspired by https://github.com/focus-trap/tabbable\nconst candidatesSelector = ['input', 'select', 'textarea', 'a[href]', 'button', '[tabindex]', 'audio[controls]', 'video[controls]', '[contenteditable]:not([contenteditable=\"false\"])'].join(',');\nfunction getTabIndex(node) {\n  const tabindexAttr = parseInt(node.getAttribute('tabindex') || '', 10);\n  if (!Number.isNaN(tabindexAttr)) {\n    return tabindexAttr;\n  }\n\n  // Browsers do not return `tabIndex` correctly for contentEditable nodes;\n  // https://issues.chromium.org/issues/41283952\n  // so if they don't have a tabindex attribute specifically set, assume it's 0.\n  // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n  //  `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n  //  yet they are still part of the regular tab order; in FF, they get a default\n  //  `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n  //  order, consider their tab index to be 0.\n  if (node.contentEditable === 'true' || (node.nodeName === 'AUDIO' || node.nodeName === 'VIDEO' || node.nodeName === 'DETAILS') && node.getAttribute('tabindex') === null) {\n    return 0;\n  }\n  return node.tabIndex;\n}\nfunction isNonTabbableRadio(node) {\n  if (node.tagName !== 'INPUT' || node.type !== 'radio') {\n    return false;\n  }\n  if (!node.name) {\n    return false;\n  }\n  const getRadio = selector => node.ownerDocument.querySelector(`input[type=\"radio\"]${selector}`);\n  let roving = getRadio(`[name=\"${node.name}\"]:checked`);\n  if (!roving) {\n    roving = getRadio(`[name=\"${node.name}\"]`);\n  }\n  return roving !== node;\n}\nfunction isNodeMatchingSelectorFocusable(node) {\n  if (node.disabled || node.tagName === 'INPUT' && node.type === 'hidden' || isNonTabbableRadio(node)) {\n    return false;\n  }\n  return true;\n}\nfunction defaultGetTabbable(root) {\n  const regularTabNodes = [];\n  const orderedTabNodes = [];\n  Array.from(root.querySelectorAll(candidatesSelector)).forEach((node, i) => {\n    const nodeTabIndex = getTabIndex(node);\n    if (nodeTabIndex === -1 || !isNodeMatchingSelectorFocusable(node)) {\n      return;\n    }\n    if (nodeTabIndex === 0) {\n      regularTabNodes.push(node);\n    } else {\n      orderedTabNodes.push({\n        documentOrder: i,\n        tabIndex: nodeTabIndex,\n        node: node\n      });\n    }\n  });\n  return orderedTabNodes.sort((a, b) => a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex).map(a => a.node).concat(regularTabNodes);\n}\nfunction defaultIsEnabled() {\n  return true;\n}\n\n/**\n * @ignore - internal component.\n */\nfunction FocusTrap(props) {\n  const {\n    children,\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableRestoreFocus = false,\n    getTabbable = defaultGetTabbable,\n    isEnabled = defaultIsEnabled,\n    open\n  } = props;\n  const ignoreNextEnforceFocus = React.useRef(false);\n  const sentinelStart = React.useRef(null);\n  const sentinelEnd = React.useRef(null);\n  const nodeToRestore = React.useRef(null);\n  const reactFocusEventTarget = React.useRef(null);\n  // This variable is useful when disableAutoFocus is true.\n  // It waits for the active element to move into the component to activate.\n  const activated = React.useRef(false);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(getReactElementRef(children), rootRef);\n  const lastKeydown = React.useRef(null);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    activated.current = !disableAutoFocus;\n  }, [disableAutoFocus, open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    if (!rootRef.current.contains(doc.activeElement)) {\n      if (!rootRef.current.hasAttribute('tabIndex')) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.error(['MUI: The modal content node does not accept focus.', 'For the benefit of assistive technologies, ' + 'the tabIndex of the node is being set to \"-1\".'].join('\\n'));\n        }\n        rootRef.current.setAttribute('tabIndex', '-1');\n      }\n      if (activated.current) {\n        rootRef.current.focus();\n      }\n    }\n    return () => {\n      // restoreLastFocus()\n      if (!disableRestoreFocus) {\n        // In IE11 it is possible for document.activeElement to be null resulting\n        // in nodeToRestore.current being null.\n        // Not all elements in IE11 have a focus method.\n        // Once IE11 support is dropped the focus() call can be unconditional.\n        if (nodeToRestore.current && nodeToRestore.current.focus) {\n          ignoreNextEnforceFocus.current = true;\n          nodeToRestore.current.focus();\n        }\n        nodeToRestore.current = null;\n      }\n    };\n    // Missing `disableRestoreFocus` which is fine.\n    // We don't support changing that prop on an open FocusTrap\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    const loopFocus = nativeEvent => {\n      lastKeydown.current = nativeEvent;\n      if (disableEnforceFocus || !isEnabled() || nativeEvent.key !== 'Tab') {\n        return;\n      }\n\n      // Make sure the next tab starts from the right place.\n      // doc.activeElement refers to the origin.\n      if (doc.activeElement === rootRef.current && nativeEvent.shiftKey) {\n        // We need to ignore the next contain as\n        // it will try to move the focus back to the rootRef element.\n        ignoreNextEnforceFocus.current = true;\n        if (sentinelEnd.current) {\n          sentinelEnd.current.focus();\n        }\n      }\n    };\n    const contain = () => {\n      const rootElement = rootRef.current;\n\n      // Cleanup functions are executed lazily in React 17.\n      // Contain can be called between the component being unmounted and its cleanup function being run.\n      if (rootElement === null) {\n        return;\n      }\n      if (!doc.hasFocus() || !isEnabled() || ignoreNextEnforceFocus.current) {\n        ignoreNextEnforceFocus.current = false;\n        return;\n      }\n\n      // The focus is already inside\n      if (rootElement.contains(doc.activeElement)) {\n        return;\n      }\n\n      // The disableEnforceFocus is set and the focus is outside of the focus trap (and sentinel nodes)\n      if (disableEnforceFocus && doc.activeElement !== sentinelStart.current && doc.activeElement !== sentinelEnd.current) {\n        return;\n      }\n\n      // if the focus event is not coming from inside the children's react tree, reset the refs\n      if (doc.activeElement !== reactFocusEventTarget.current) {\n        reactFocusEventTarget.current = null;\n      } else if (reactFocusEventTarget.current !== null) {\n        return;\n      }\n      if (!activated.current) {\n        return;\n      }\n      let tabbable = [];\n      if (doc.activeElement === sentinelStart.current || doc.activeElement === sentinelEnd.current) {\n        tabbable = getTabbable(rootRef.current);\n      }\n\n      // one of the sentinel nodes was focused, so move the focus\n      // to the first/last tabbable element inside the focus trap\n      if (tabbable.length > 0) {\n        const isShiftTab = Boolean(lastKeydown.current?.shiftKey && lastKeydown.current?.key === 'Tab');\n        const focusNext = tabbable[0];\n        const focusPrevious = tabbable[tabbable.length - 1];\n        if (typeof focusNext !== 'string' && typeof focusPrevious !== 'string') {\n          if (isShiftTab) {\n            focusPrevious.focus();\n          } else {\n            focusNext.focus();\n          }\n        }\n        // no tabbable elements in the trap focus or the focus was outside of the focus trap\n      } else {\n        rootElement.focus();\n      }\n    };\n    doc.addEventListener('focusin', contain);\n    doc.addEventListener('keydown', loopFocus, true);\n\n    // With Edge, Safari and Firefox, no focus related events are fired when the focused area stops being a focused area.\n    // for example https://bugzilla.mozilla.org/show_bug.cgi?id=559561.\n    // Instead, we can look if the active element was restored on the BODY element.\n    //\n    // The whatwg spec defines how the browser should behave but does not explicitly mention any events:\n    // https://html.spec.whatwg.org/multipage/interaction.html#focus-fixup-rule.\n    const interval = setInterval(() => {\n      if (doc.activeElement && doc.activeElement.tagName === 'BODY') {\n        contain();\n      }\n    }, 50);\n    return () => {\n      clearInterval(interval);\n      doc.removeEventListener('focusin', contain);\n      doc.removeEventListener('keydown', loopFocus, true);\n    };\n  }, [disableAutoFocus, disableEnforceFocus, disableRestoreFocus, isEnabled, open, getTabbable]);\n  const onFocus = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n    reactFocusEventTarget.current = event.target;\n    const childrenPropsHandler = children.props.onFocus;\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const handleFocusSentinel = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelStart,\n      \"data-testid\": \"sentinelStart\"\n    }), /*#__PURE__*/React.cloneElement(children, {\n      ref: handleRef,\n      onFocus\n    }), /*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelEnd,\n      \"data-testid\": \"sentinelEnd\"\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? FocusTrap.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef,\n  /**\n   * If `true`, the focus trap will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any focus trap children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the focus trap less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the focus trap will not prevent focus from leaving the focus trap while open.\n   *\n   * Generally this should never be set to `true` as it makes the focus trap less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, the focus trap will not restore focus to previously focused element once\n   * focus trap is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Returns an array of ordered tabbable nodes (i.e. in tab order) within the root.\n   * For instance, you can provide the \"tabbable\" npm dependency.\n   * @param {HTMLElement} root\n   */\n  getTabbable: PropTypes.func,\n  /**\n   * This prop extends the `open` prop.\n   * It allows to toggle the open state without having to wait for a rerender when changing the `open` prop.\n   * This prop should be memoized.\n   * It can be used to support multiple focus trap mounted at the same time.\n   * @default function defaultIsEnabled(): boolean {\n   *   return true;\n   * }\n   */\n  isEnabled: PropTypes.func,\n  /**\n   * If `true`, focus is locked.\n   */\n  open: PropTypes.bool.isRequired\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  FocusTrap['propTypes' + ''] = exactProp(FocusTrap.propTypes);\n}\nexport default FocusTrap;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,EAAEC,mBAAmB,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,2BAA2B,IAAIC,kBAAkB,QAAQ,YAAY;AAC1L,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D;AACA,MAAMC,kBAAkB,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kDAAkD,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AACjM,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,MAAMC,YAAY,GAAGC,QAAQ,CAACF,IAAI,CAACG,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;EACtE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACJ,YAAY,CAAC,EAAE;IAC/B,OAAOA,YAAY;EACrB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAID,IAAI,CAACM,eAAe,KAAK,MAAM,IAAI,CAACN,IAAI,CAACO,QAAQ,KAAK,OAAO,IAAIP,IAAI,CAACO,QAAQ,KAAK,OAAO,IAAIP,IAAI,CAACO,QAAQ,KAAK,SAAS,KAAKP,IAAI,CAACG,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;IACxK,OAAO,CAAC;EACV;EACA,OAAOH,IAAI,CAACQ,QAAQ;AACtB;AACA,SAASC,kBAAkBA,CAACT,IAAI,EAAE;EAChC,IAAIA,IAAI,CAACU,OAAO,KAAK,OAAO,IAAIV,IAAI,CAACW,IAAI,KAAK,OAAO,EAAE;IACrD,OAAO,KAAK;EACd;EACA,IAAI,CAACX,IAAI,CAACY,IAAI,EAAE;IACd,OAAO,KAAK;EACd;EACA,MAAMC,QAAQ,GAAGC,QAAQ,IAAId,IAAI,CAACV,aAAa,CAACyB,aAAa,CAAC,sBAAsBD,QAAQ,EAAE,CAAC;EAC/F,IAAIE,MAAM,GAAGH,QAAQ,CAAC,UAAUb,IAAI,CAACY,IAAI,YAAY,CAAC;EACtD,IAAI,CAACI,MAAM,EAAE;IACXA,MAAM,GAAGH,QAAQ,CAAC,UAAUb,IAAI,CAACY,IAAI,IAAI,CAAC;EAC5C;EACA,OAAOI,MAAM,KAAKhB,IAAI;AACxB;AACA,SAASiB,+BAA+BA,CAACjB,IAAI,EAAE;EAC7C,IAAIA,IAAI,CAACkB,QAAQ,IAAIlB,IAAI,CAACU,OAAO,KAAK,OAAO,IAAIV,IAAI,CAACW,IAAI,KAAK,QAAQ,IAAIF,kBAAkB,CAACT,IAAI,CAAC,EAAE;IACnG,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACA,SAASmB,kBAAkBA,CAACC,IAAI,EAAE;EAChC,MAAMC,eAAe,GAAG,EAAE;EAC1B,MAAMC,eAAe,GAAG,EAAE;EAC1BC,KAAK,CAACC,IAAI,CAACJ,IAAI,CAACK,gBAAgB,CAAC5B,kBAAkB,CAAC,CAAC,CAAC6B,OAAO,CAAC,CAAC1B,IAAI,EAAE2B,CAAC,KAAK;IACzE,MAAMC,YAAY,GAAG7B,WAAW,CAACC,IAAI,CAAC;IACtC,IAAI4B,YAAY,KAAK,CAAC,CAAC,IAAI,CAACX,+BAA+B,CAACjB,IAAI,CAAC,EAAE;MACjE;IACF;IACA,IAAI4B,YAAY,KAAK,CAAC,EAAE;MACtBP,eAAe,CAACQ,IAAI,CAAC7B,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLsB,eAAe,CAACO,IAAI,CAAC;QACnBC,aAAa,EAAEH,CAAC;QAChBnB,QAAQ,EAAEoB,YAAY;QACtB5B,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOsB,eAAe,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxB,QAAQ,KAAKyB,CAAC,CAACzB,QAAQ,GAAGwB,CAAC,CAACF,aAAa,GAAGG,CAAC,CAACH,aAAa,GAAGE,CAAC,CAACxB,QAAQ,GAAGyB,CAAC,CAACzB,QAAQ,CAAC,CAAC0B,GAAG,CAACF,CAAC,IAAIA,CAAC,CAAChC,IAAI,CAAC,CAACmC,MAAM,CAACd,eAAe,CAAC;AACzK;AACA,SAASe,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,MAAM;IACJC,QAAQ;IACRC,gBAAgB,GAAG,KAAK;IACxBC,mBAAmB,GAAG,KAAK;IAC3BC,mBAAmB,GAAG,KAAK;IAC3BC,WAAW,GAAGxB,kBAAkB;IAChCyB,SAAS,GAAGR,gBAAgB;IAC5BS;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,sBAAsB,GAAG/D,KAAK,CAACgE,MAAM,CAAC,KAAK,CAAC;EAClD,MAAMC,aAAa,GAAGjE,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EACxC,MAAME,WAAW,GAAGlE,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMG,aAAa,GAAGnE,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMI,qBAAqB,GAAGpE,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EAChD;EACA;EACA,MAAMK,SAAS,GAAGrE,KAAK,CAACgE,MAAM,CAAC,KAAK,CAAC;EACrC,MAAMM,OAAO,GAAGtE,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMO,SAAS,GAAGlE,UAAU,CAACI,kBAAkB,CAAC+C,QAAQ,CAAC,EAAEc,OAAO,CAAC;EACnE,MAAME,WAAW,GAAGxE,KAAK,CAACgE,MAAM,CAAC,IAAI,CAAC;EACtChE,KAAK,CAACyE,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACX,IAAI,IAAI,CAACQ,OAAO,CAACI,OAAO,EAAE;MAC7B;IACF;IACAL,SAAS,CAACK,OAAO,GAAG,CAACjB,gBAAgB;EACvC,CAAC,EAAE,CAACA,gBAAgB,EAAEK,IAAI,CAAC,CAAC;EAC5B9D,KAAK,CAACyE,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACX,IAAI,IAAI,CAACQ,OAAO,CAACI,OAAO,EAAE;MAC7B;IACF;IACA,MAAMC,GAAG,GAAGpE,aAAa,CAAC+D,OAAO,CAACI,OAAO,CAAC;IAC1C,IAAI,CAACJ,OAAO,CAACI,OAAO,CAACE,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,EAAE;MAChD,IAAI,CAACP,OAAO,CAACI,OAAO,CAACI,YAAY,CAAC,UAAU,CAAC,EAAE;QAC7C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCC,OAAO,CAACC,KAAK,CAAC,CAAC,oDAAoD,EAAE,6CAA6C,GAAG,gDAAgD,CAAC,CAACpE,IAAI,CAAC,IAAI,CAAC,CAAC;QACpL;QACAuD,OAAO,CAACI,OAAO,CAACU,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;MAChD;MACA,IAAIf,SAAS,CAACK,OAAO,EAAE;QACrBJ,OAAO,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;MACzB;IACF;IACA,OAAO,MAAM;MACX;MACA,IAAI,CAAC1B,mBAAmB,EAAE;QACxB;QACA;QACA;QACA;QACA,IAAIQ,aAAa,CAACO,OAAO,IAAIP,aAAa,CAACO,OAAO,CAACW,KAAK,EAAE;UACxDtB,sBAAsB,CAACW,OAAO,GAAG,IAAI;UACrCP,aAAa,CAACO,OAAO,CAACW,KAAK,CAAC,CAAC;QAC/B;QACAlB,aAAa,CAACO,OAAO,GAAG,IAAI;MAC9B;IACF,CAAC;IACD;IACA;IACA;EACF,CAAC,EAAE,CAACZ,IAAI,CAAC,CAAC;EACV9D,KAAK,CAACyE,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACX,IAAI,IAAI,CAACQ,OAAO,CAACI,OAAO,EAAE;MAC7B;IACF;IACA,MAAMC,GAAG,GAAGpE,aAAa,CAAC+D,OAAO,CAACI,OAAO,CAAC;IAC1C,MAAMY,SAAS,GAAGC,WAAW,IAAI;MAC/Bf,WAAW,CAACE,OAAO,GAAGa,WAAW;MACjC,IAAI7B,mBAAmB,IAAI,CAACG,SAAS,CAAC,CAAC,IAAI0B,WAAW,CAACC,GAAG,KAAK,KAAK,EAAE;QACpE;MACF;;MAEA;MACA;MACA,IAAIb,GAAG,CAACE,aAAa,KAAKP,OAAO,CAACI,OAAO,IAAIa,WAAW,CAACE,QAAQ,EAAE;QACjE;QACA;QACA1B,sBAAsB,CAACW,OAAO,GAAG,IAAI;QACrC,IAAIR,WAAW,CAACQ,OAAO,EAAE;UACvBR,WAAW,CAACQ,OAAO,CAACW,KAAK,CAAC,CAAC;QAC7B;MACF;IACF,CAAC;IACD,MAAMK,OAAO,GAAGA,CAAA,KAAM;MACpB,MAAMC,WAAW,GAAGrB,OAAO,CAACI,OAAO;;MAEnC;MACA;MACA,IAAIiB,WAAW,KAAK,IAAI,EAAE;QACxB;MACF;MACA,IAAI,CAAChB,GAAG,CAACiB,QAAQ,CAAC,CAAC,IAAI,CAAC/B,SAAS,CAAC,CAAC,IAAIE,sBAAsB,CAACW,OAAO,EAAE;QACrEX,sBAAsB,CAACW,OAAO,GAAG,KAAK;QACtC;MACF;;MAEA;MACA,IAAIiB,WAAW,CAACf,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,EAAE;QAC3C;MACF;;MAEA;MACA,IAAInB,mBAAmB,IAAIiB,GAAG,CAACE,aAAa,KAAKZ,aAAa,CAACS,OAAO,IAAIC,GAAG,CAACE,aAAa,KAAKX,WAAW,CAACQ,OAAO,EAAE;QACnH;MACF;;MAEA;MACA,IAAIC,GAAG,CAACE,aAAa,KAAKT,qBAAqB,CAACM,OAAO,EAAE;QACvDN,qBAAqB,CAACM,OAAO,GAAG,IAAI;MACtC,CAAC,MAAM,IAAIN,qBAAqB,CAACM,OAAO,KAAK,IAAI,EAAE;QACjD;MACF;MACA,IAAI,CAACL,SAAS,CAACK,OAAO,EAAE;QACtB;MACF;MACA,IAAImB,QAAQ,GAAG,EAAE;MACjB,IAAIlB,GAAG,CAACE,aAAa,KAAKZ,aAAa,CAACS,OAAO,IAAIC,GAAG,CAACE,aAAa,KAAKX,WAAW,CAACQ,OAAO,EAAE;QAC5FmB,QAAQ,GAAGjC,WAAW,CAACU,OAAO,CAACI,OAAO,CAAC;MACzC;;MAEA;MACA;MACA,IAAImB,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACvB,MAAMC,UAAU,GAAGC,OAAO,CAACxB,WAAW,CAACE,OAAO,EAAEe,QAAQ,IAAIjB,WAAW,CAACE,OAAO,EAAEc,GAAG,KAAK,KAAK,CAAC;QAC/F,MAAMS,SAAS,GAAGJ,QAAQ,CAAC,CAAC,CAAC;QAC7B,MAAMK,aAAa,GAAGL,QAAQ,CAACA,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;QACnD,IAAI,OAAOG,SAAS,KAAK,QAAQ,IAAI,OAAOC,aAAa,KAAK,QAAQ,EAAE;UACtE,IAAIH,UAAU,EAAE;YACdG,aAAa,CAACb,KAAK,CAAC,CAAC;UACvB,CAAC,MAAM;YACLY,SAAS,CAACZ,KAAK,CAAC,CAAC;UACnB;QACF;QACA;MACF,CAAC,MAAM;QACLM,WAAW,CAACN,KAAK,CAAC,CAAC;MACrB;IACF,CAAC;IACDV,GAAG,CAACwB,gBAAgB,CAAC,SAAS,EAAET,OAAO,CAAC;IACxCf,GAAG,CAACwB,gBAAgB,CAAC,SAAS,EAAEb,SAAS,EAAE,IAAI,CAAC;;IAEhD;IACA;IACA;IACA;IACA;IACA;IACA,MAAMc,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAI1B,GAAG,CAACE,aAAa,IAAIF,GAAG,CAACE,aAAa,CAAClD,OAAO,KAAK,MAAM,EAAE;QAC7D+D,OAAO,CAAC,CAAC;MACX;IACF,CAAC,EAAE,EAAE,CAAC;IACN,OAAO,MAAM;MACXY,aAAa,CAACF,QAAQ,CAAC;MACvBzB,GAAG,CAAC4B,mBAAmB,CAAC,SAAS,EAAEb,OAAO,CAAC;MAC3Cf,GAAG,CAAC4B,mBAAmB,CAAC,SAAS,EAAEjB,SAAS,EAAE,IAAI,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CAAC7B,gBAAgB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEE,SAAS,EAAEC,IAAI,EAAEF,WAAW,CAAC,CAAC;EAC9F,MAAM4C,OAAO,GAAGC,KAAK,IAAI;IACvB,IAAItC,aAAa,CAACO,OAAO,KAAK,IAAI,EAAE;MAClCP,aAAa,CAACO,OAAO,GAAG+B,KAAK,CAACC,aAAa;IAC7C;IACArC,SAAS,CAACK,OAAO,GAAG,IAAI;IACxBN,qBAAqB,CAACM,OAAO,GAAG+B,KAAK,CAACE,MAAM;IAC5C,MAAMC,oBAAoB,GAAGpD,QAAQ,CAACD,KAAK,CAACiD,OAAO;IACnD,IAAII,oBAAoB,EAAE;MACxBA,oBAAoB,CAACH,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,MAAMI,mBAAmB,GAAGJ,KAAK,IAAI;IACnC,IAAItC,aAAa,CAACO,OAAO,KAAK,IAAI,EAAE;MAClCP,aAAa,CAACO,OAAO,GAAG+B,KAAK,CAACC,aAAa;IAC7C;IACArC,SAAS,CAACK,OAAO,GAAG,IAAI;EAC1B,CAAC;EACD,OAAO,aAAa7D,KAAK,CAACb,KAAK,CAAC8G,QAAQ,EAAE;IACxCtD,QAAQ,EAAE,CAAC,aAAa7C,IAAI,CAAC,KAAK,EAAE;MAClCc,QAAQ,EAAEqC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;MACvB0C,OAAO,EAAEK,mBAAmB;MAC5BE,GAAG,EAAE9C,aAAa;MAClB,aAAa,EAAE;IACjB,CAAC,CAAC,EAAE,aAAajE,KAAK,CAACgH,YAAY,CAACxD,QAAQ,EAAE;MAC5CuD,GAAG,EAAExC,SAAS;MACdiC;IACF,CAAC,CAAC,EAAE,aAAa7F,IAAI,CAAC,KAAK,EAAE;MAC3Bc,QAAQ,EAAEqC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;MACvB0C,OAAO,EAAEK,mBAAmB;MAC5BE,GAAG,EAAE7C,WAAW;MAChB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACAa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3B,SAAS,CAAC2D,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEzD,QAAQ,EAAErD,mBAAmB;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsD,gBAAgB,EAAExD,SAAS,CAACiH,IAAI;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;EACExD,mBAAmB,EAAEzD,SAAS,CAACiH,IAAI;EACnC;AACF;AACA;AACA;AACA;EACEvD,mBAAmB,EAAE1D,SAAS,CAACiH,IAAI;EACnC;AACF;AACA;AACA;AACA;EACEtD,WAAW,EAAE3D,SAAS,CAACkH,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtD,SAAS,EAAE5D,SAAS,CAACkH,IAAI;EACzB;AACF;AACA;EACErD,IAAI,EAAE7D,SAAS,CAACiH,IAAI,CAACE;AACvB,CAAC,GAAG,KAAK,CAAC;AACV,IAAIrC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACA3B,SAAS,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGpD,SAAS,CAACoD,SAAS,CAAC2D,SAAS,CAAC;AAC9D;AACA,eAAe3D,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}