{"ast": null, "code": "'use client';\n\nimport createTheme from \"../createTheme/index.js\";\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\nexport const systemDefaultTheme = createTheme();\nfunction useTheme() {\n  let defaultTheme = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : systemDefaultTheme;\n  return useThemeWithoutDefault(defaultTheme);\n}\nexport default useTheme;", "map": {"version": 3, "names": ["createTheme", "useThemeWithoutDefault", "systemDefaultTheme", "useTheme", "defaultTheme", "arguments", "length", "undefined"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/system/esm/useTheme/useTheme.js"], "sourcesContent": ["'use client';\n\nimport createTheme from \"../createTheme/index.js\";\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\nexport const systemDefaultTheme = createTheme();\nfunction useTheme(defaultTheme = systemDefaultTheme) {\n  return useThemeWithoutDefault(defaultTheme);\n}\nexport default useTheme;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,WAAW,MAAM,yBAAyB;AACjD,OAAOC,sBAAsB,MAAM,oCAAoC;AACvE,OAAO,MAAMC,kBAAkB,GAAGF,WAAW,CAAC,CAAC;AAC/C,SAASG,QAAQA,CAAA,EAAoC;EAAA,IAAnCC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGH,kBAAkB;EACjD,OAAOD,sBAAsB,CAACG,YAAY,CAAC;AAC7C;AACA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}