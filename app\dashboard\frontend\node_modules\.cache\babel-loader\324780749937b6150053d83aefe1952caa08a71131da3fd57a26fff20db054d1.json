{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useTimeout as useTimeout } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\n/**\n * The basic building block for creating custom snackbar.\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/#hook)\n *\n * API:\n *\n * - [useSnackbar API](https://mui.com/base-ui/react-snackbar/hooks-api/#use-snackbar)\n */\nfunction useSnackbar() {\n  let parameters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    autoHideDuration = null,\n    disableWindowBlurListener = false,\n    onClose,\n    open,\n    resumeHideDuration\n  } = parameters;\n  const timerAutoHide = useTimeout();\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (!nativeEvent.defaultPrevented) {\n        if (nativeEvent.key === 'Escape') {\n          // not calling `preventDefault` since we don't know if people may ignore this event e.g. a permanently open snackbar\n          onClose?.(nativeEvent, 'escapeKeyDown');\n        }\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [open, onClose]);\n  const handleClose = useEventCallback((event, reason) => {\n    onClose?.(event, reason);\n  });\n  const setAutoHideTimer = useEventCallback(autoHideDurationParam => {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n    timerAutoHide.start(autoHideDurationParam, () => {\n      handleClose(null, 'timeout');\n    });\n  });\n  React.useEffect(() => {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n    return timerAutoHide.clear;\n  }, [open, autoHideDuration, setAutoHideTimer, timerAutoHide]);\n  const handleClickAway = event => {\n    onClose?.(event, 'clickaway');\n  };\n\n  // Pause the timer when the user is interacting with the Snackbar\n  // or when the user hide the window.\n  const handlePause = timerAutoHide.clear;\n\n  // Restart the timer when the user is no longer interacting with the Snackbar\n  // or when the window is shown back.\n  const handleResume = React.useCallback(() => {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n  const createHandleBlur = otherHandlers => event => {\n    const onBlurCallback = otherHandlers.onBlur;\n    onBlurCallback?.(event);\n    handleResume();\n  };\n  const createHandleFocus = otherHandlers => event => {\n    const onFocusCallback = otherHandlers.onFocus;\n    onFocusCallback?.(event);\n    handlePause();\n  };\n  const createMouseEnter = otherHandlers => event => {\n    const onMouseEnterCallback = otherHandlers.onMouseEnter;\n    onMouseEnterCallback?.(event);\n    handlePause();\n  };\n  const createMouseLeave = otherHandlers => event => {\n    const onMouseLeaveCallback = otherHandlers.onMouseLeave;\n    onMouseLeaveCallback?.(event);\n    handleResume();\n  };\n  React.useEffect(() => {\n    // TODO: window global should be refactored here\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return () => {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n    return undefined;\n  }, [disableWindowBlurListener, open, handleResume, handlePause]);\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = {\n      ...extractEventHandlers(parameters),\n      ...extractEventHandlers(externalProps)\n    };\n    return {\n      // ClickAwayListener adds an `onClick` prop which results in the alert not being announced.\n      // See https://github.com/mui/material-ui/issues/29080\n      role: 'presentation',\n      ...externalProps,\n      ...externalEventHandlers,\n      onBlur: createHandleBlur(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onMouseEnter: createMouseEnter(externalEventHandlers),\n      onMouseLeave: createMouseLeave(externalEventHandlers)\n    };\n  };\n  return {\n    getRootProps,\n    onClickAway: handleClickAway\n  };\n}\nexport default useSnackbar;", "map": {"version": 3, "names": ["React", "unstable_useEventCallback", "useEventCallback", "unstable_useTimeout", "useTimeout", "extractEventHandlers", "useSnackbar", "parameters", "arguments", "length", "undefined", "autoHideDuration", "disableWindowBlurListener", "onClose", "open", "resumeHideDuration", "timerAutoHide", "useEffect", "handleKeyDown", "nativeEvent", "defaultPrevented", "key", "document", "addEventListener", "removeEventListener", "handleClose", "event", "reason", "setAutoHideTimer", "autoHideDurationParam", "start", "clear", "handleClickAway", "handlePause", "handleResume", "useCallback", "createHandleBlur", "otherHandlers", "onBlurCallback", "onBlur", "createHandleFocus", "onFocusCallback", "onFocus", "createMouseEnter", "onMouseEnterCallback", "onMouseEnter", "createMouseLeave", "onMouseLeaveCallback", "onMouseLeave", "window", "getRootProps", "externalProps", "externalEventHandlers", "role", "onClickAway"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/Snackbar/useSnackbar.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useTimeout as useTimeout } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\n/**\n * The basic building block for creating custom snackbar.\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/#hook)\n *\n * API:\n *\n * - [useSnackbar API](https://mui.com/base-ui/react-snackbar/hooks-api/#use-snackbar)\n */\nfunction useSnackbar(parameters = {}) {\n  const {\n    autoHideDuration = null,\n    disableWindowBlurListener = false,\n    onClose,\n    open,\n    resumeHideDuration\n  } = parameters;\n  const timerAutoHide = useTimeout();\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (!nativeEvent.defaultPrevented) {\n        if (nativeEvent.key === 'Escape') {\n          // not calling `preventDefault` since we don't know if people may ignore this event e.g. a permanently open snackbar\n          onClose?.(nativeEvent, 'escapeKeyDown');\n        }\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [open, onClose]);\n  const handleClose = useEventCallback((event, reason) => {\n    onClose?.(event, reason);\n  });\n  const setAutoHideTimer = useEventCallback(autoHideDurationParam => {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n    timerAutoHide.start(autoHideDurationParam, () => {\n      handleClose(null, 'timeout');\n    });\n  });\n  React.useEffect(() => {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n    return timerAutoHide.clear;\n  }, [open, autoHideDuration, setAutoHideTimer, timerAutoHide]);\n  const handleClickAway = event => {\n    onClose?.(event, 'clickaway');\n  };\n\n  // Pause the timer when the user is interacting with the Snackbar\n  // or when the user hide the window.\n  const handlePause = timerAutoHide.clear;\n\n  // Restart the timer when the user is no longer interacting with the Snackbar\n  // or when the window is shown back.\n  const handleResume = React.useCallback(() => {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n  const createHandleBlur = otherHandlers => event => {\n    const onBlurCallback = otherHandlers.onBlur;\n    onBlurCallback?.(event);\n    handleResume();\n  };\n  const createHandleFocus = otherHandlers => event => {\n    const onFocusCallback = otherHandlers.onFocus;\n    onFocusCallback?.(event);\n    handlePause();\n  };\n  const createMouseEnter = otherHandlers => event => {\n    const onMouseEnterCallback = otherHandlers.onMouseEnter;\n    onMouseEnterCallback?.(event);\n    handlePause();\n  };\n  const createMouseLeave = otherHandlers => event => {\n    const onMouseLeaveCallback = otherHandlers.onMouseLeave;\n    onMouseLeaveCallback?.(event);\n    handleResume();\n  };\n  React.useEffect(() => {\n    // TODO: window global should be refactored here\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return () => {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n    return undefined;\n  }, [disableWindowBlurListener, open, handleResume, handlePause]);\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = {\n      ...extractEventHandlers(parameters),\n      ...extractEventHandlers(externalProps)\n    };\n    return {\n      // ClickAwayListener adds an `onClick` prop which results in the alert not being announced.\n      // See https://github.com/mui/material-ui/issues/29080\n      role: 'presentation',\n      ...externalProps,\n      ...externalEventHandlers,\n      onBlur: createHandleBlur(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onMouseEnter: createMouseEnter(externalEventHandlers),\n      onMouseLeave: createMouseLeave(externalEventHandlers)\n    };\n  };\n  return {\n    getRootProps,\n    onClickAway: handleClickAway\n  };\n}\nexport default useSnackbar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,IAAIC,gBAAgB,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC7G,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAkB;EAAA,IAAjBC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAClC,MAAM;IACJG,gBAAgB,GAAG,IAAI;IACvBC,yBAAyB,GAAG,KAAK;IACjCC,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,aAAa,GAAGZ,UAAU,CAAC,CAAC;EAClCJ,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAI,CAACH,IAAI,EAAE;MACT,OAAOJ,SAAS;IAClB;;IAEA;AACJ;AACA;IACI,SAASQ,aAAaA,CAACC,WAAW,EAAE;MAClC,IAAI,CAACA,WAAW,CAACC,gBAAgB,EAAE;QACjC,IAAID,WAAW,CAACE,GAAG,KAAK,QAAQ,EAAE;UAChC;UACAR,OAAO,GAAGM,WAAW,EAAE,eAAe,CAAC;QACzC;MACF;IACF;IACAG,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEL,aAAa,CAAC;IACnD,OAAO,MAAM;MACXI,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACJ,IAAI,EAAED,OAAO,CAAC,CAAC;EACnB,MAAMY,WAAW,GAAGvB,gBAAgB,CAAC,CAACwB,KAAK,EAAEC,MAAM,KAAK;IACtDd,OAAO,GAAGa,KAAK,EAAEC,MAAM,CAAC;EAC1B,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAG1B,gBAAgB,CAAC2B,qBAAqB,IAAI;IACjE,IAAI,CAAChB,OAAO,IAAIgB,qBAAqB,IAAI,IAAI,EAAE;MAC7C;IACF;IACAb,aAAa,CAACc,KAAK,CAACD,qBAAqB,EAAE,MAAM;MAC/CJ,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EACFzB,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAIH,IAAI,EAAE;MACRc,gBAAgB,CAACjB,gBAAgB,CAAC;IACpC;IACA,OAAOK,aAAa,CAACe,KAAK;EAC5B,CAAC,EAAE,CAACjB,IAAI,EAAEH,gBAAgB,EAAEiB,gBAAgB,EAAEZ,aAAa,CAAC,CAAC;EAC7D,MAAMgB,eAAe,GAAGN,KAAK,IAAI;IAC/Bb,OAAO,GAAGa,KAAK,EAAE,WAAW,CAAC;EAC/B,CAAC;;EAED;EACA;EACA,MAAMO,WAAW,GAAGjB,aAAa,CAACe,KAAK;;EAEvC;EACA;EACA,MAAMG,YAAY,GAAGlC,KAAK,CAACmC,WAAW,CAAC,MAAM;IAC3C,IAAIxB,gBAAgB,IAAI,IAAI,EAAE;MAC5BiB,gBAAgB,CAACb,kBAAkB,IAAI,IAAI,GAAGA,kBAAkB,GAAGJ,gBAAgB,GAAG,GAAG,CAAC;IAC5F;EACF,CAAC,EAAE,CAACA,gBAAgB,EAAEI,kBAAkB,EAAEa,gBAAgB,CAAC,CAAC;EAC5D,MAAMQ,gBAAgB,GAAGC,aAAa,IAAIX,KAAK,IAAI;IACjD,MAAMY,cAAc,GAAGD,aAAa,CAACE,MAAM;IAC3CD,cAAc,GAAGZ,KAAK,CAAC;IACvBQ,YAAY,CAAC,CAAC;EAChB,CAAC;EACD,MAAMM,iBAAiB,GAAGH,aAAa,IAAIX,KAAK,IAAI;IAClD,MAAMe,eAAe,GAAGJ,aAAa,CAACK,OAAO;IAC7CD,eAAe,GAAGf,KAAK,CAAC;IACxBO,WAAW,CAAC,CAAC;EACf,CAAC;EACD,MAAMU,gBAAgB,GAAGN,aAAa,IAAIX,KAAK,IAAI;IACjD,MAAMkB,oBAAoB,GAAGP,aAAa,CAACQ,YAAY;IACvDD,oBAAoB,GAAGlB,KAAK,CAAC;IAC7BO,WAAW,CAAC,CAAC;EACf,CAAC;EACD,MAAMa,gBAAgB,GAAGT,aAAa,IAAIX,KAAK,IAAI;IACjD,MAAMqB,oBAAoB,GAAGV,aAAa,CAACW,YAAY;IACvDD,oBAAoB,GAAGrB,KAAK,CAAC;IAC7BQ,YAAY,CAAC,CAAC;EAChB,CAAC;EACDlC,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACL,yBAAyB,IAAIE,IAAI,EAAE;MACtCmC,MAAM,CAAC1B,gBAAgB,CAAC,OAAO,EAAEW,YAAY,CAAC;MAC9Ce,MAAM,CAAC1B,gBAAgB,CAAC,MAAM,EAAEU,WAAW,CAAC;MAC5C,OAAO,MAAM;QACXgB,MAAM,CAACzB,mBAAmB,CAAC,OAAO,EAAEU,YAAY,CAAC;QACjDe,MAAM,CAACzB,mBAAmB,CAAC,MAAM,EAAES,WAAW,CAAC;MACjD,CAAC;IACH;IACA,OAAOvB,SAAS;EAClB,CAAC,EAAE,CAACE,yBAAyB,EAAEE,IAAI,EAAEoB,YAAY,EAAED,WAAW,CAAC,CAAC;EAChE,MAAMiB,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBC,aAAa,GAAA3C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACtC,MAAM4C,qBAAqB,GAAG;MAC5B,GAAG/C,oBAAoB,CAACE,UAAU,CAAC;MACnC,GAAGF,oBAAoB,CAAC8C,aAAa;IACvC,CAAC;IACD,OAAO;MACL;MACA;MACAE,IAAI,EAAE,cAAc;MACpB,GAAGF,aAAa;MAChB,GAAGC,qBAAqB;MACxBb,MAAM,EAAEH,gBAAgB,CAACgB,qBAAqB,CAAC;MAC/CV,OAAO,EAAEF,iBAAiB,CAACY,qBAAqB,CAAC;MACjDP,YAAY,EAAEF,gBAAgB,CAACS,qBAAqB,CAAC;MACrDJ,YAAY,EAAEF,gBAAgB,CAACM,qBAAqB;IACtD,CAAC;EACH,CAAC;EACD,OAAO;IACLF,YAAY;IACZI,WAAW,EAAEtB;EACf,CAAC;AACH;AACA,eAAe1B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}