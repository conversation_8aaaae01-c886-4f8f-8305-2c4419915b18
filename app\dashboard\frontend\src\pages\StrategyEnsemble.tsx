import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Alert,
  Chip,
  LinearProgress,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  IconButton,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  BarChart,
  Bar,
  Area,
  AreaChart,
} from 'recharts';
import { useAuth } from '../contexts/AuthContext';
import { ensembleAPI } from '../services/api';
import ErrorBoundary from '../components/ErrorBoundary';
import LoadingFallback from '../components/LoadingFallback';
import DashboardLayout from '../components/DashboardLayout';
import { 
  websocketService, 
  WebSocketEventType,
  EnsembleWeightsEvent,
  EnsembleSignalsEvent,
  EnsemblePerformanceEvent,
  EnsembleRiskEvent,
  EnsembleModelEvent
} from '../services/websocket';
import {
  EnsembleData,
  StrategyWeight,
  StrategySignal,
  EnsemblePortfolioMetrics,
  StrategyCorrelation,
  RiskLimit,
  MLModelStatus,
  PerformanceDataPoint,
} from '../types';

// Color schemes for visualizations
const STRATEGY_COLORS = {
  'Grid Trading': '#2196F3',
  'Technical Analysis': '#4CAF50',
  'Trend Following': '#FF9800',
  'Mean Reversion': '#9C27B0',
  'Momentum': '#F44336',
  'Arbitrage': '#00BCD4',
} as const;

const RISK_STATUS_COLORS = {
  normal: '#4CAF50',
  warning: '#FF9800',
  critical: '#F44336',
} as const;

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ensemble-tabpanel-${index}`}
      aria-labelledby={`ensemble-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const StrategyEnsemble: React.FC = () => {
  const { state: authState } = useAuth();
  const [ensembleData, setEnsembleData] = useState<EnsembleData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Mock data generator for when API is unavailable
  const generateMockData = (): EnsembleData => {
    return {
      strategy_weights: [
        { strategy: 'Grid Trading', weight: 0.25, confidence: 0.85, last_updated: new Date().toISOString() },
        { strategy: 'Technical Analysis', weight: 0.3, confidence: 0.92, last_updated: new Date().toISOString() },
        { strategy: 'Trend Following', weight: 0.2, confidence: 0.78, last_updated: new Date().toISOString() },
        { strategy: 'Mean Reversion', weight: 0.15, confidence: 0.88, last_updated: new Date().toISOString() },
        { strategy: 'Momentum', weight: 0.1, confidence: 0.75, last_updated: new Date().toISOString() },
      ],
      current_signals: [
        { strategy: 'Grid Trading', signal: 'buy', strength: 0.7, confidence: 0.85, timestamp: new Date().toISOString() },
        { strategy: 'Technical Analysis', signal: 'hold', strength: 0.3, confidence: 0.6, timestamp: new Date().toISOString() },
        { strategy: 'Trend Following', signal: 'buy', strength: 0.8, confidence: 0.9, timestamp: new Date().toISOString() },
      ],
      signal_conflicts: [],
      portfolio_metrics: {
        total_return: 0.125,
        sharpe_ratio: 1.45,
        max_drawdown: -0.08,
        win_rate: 0.68,
        profit_factor: 1.8,
        volatility: 0.15,
        alpha: 0.02,
        beta: 0.85,
        sortino_ratio: 1.9,
        calmar_ratio: 1.56,
        last_updated: new Date().toISOString(),
      },
      strategy_correlations: [
        { strategy_a: 'Grid Trading', strategy_b: 'Technical Analysis', correlation: 0.3, p_value: 0.05 },
        { strategy_a: 'Trend Following', strategy_b: 'Momentum', correlation: 0.7, p_value: 0.01 },
      ],
      risk_limits: [
        { metric: 'Portfolio Risk', current_value: 15, limit_value: 20, utilization_pct: 75, status: 'normal' },
        { metric: 'Daily Drawdown', current_value: 3, limit_value: 5, utilization_pct: 60, status: 'normal' },
        { metric: 'Position Size', current_value: 8, limit_value: 10, utilization_pct: 80, status: 'warning' },
      ],
      ml_model_status: {
        model_name: 'Ensemble Weight Optimizer',
        version: 'v1.2.3',
        accuracy: 0.78,
        confidence: 0.85,
        last_trained: new Date(Date.now() - 86400000).toISOString(),
        next_training: new Date(Date.now() + 86400000).toISOString(),
        status: 'active',
        feature_count: 24,
        training_samples: 10000,
      },
      performance_history: Array.from({ length: 30 }, (_, i) => ({
        timestamp: new Date(Date.now() - (29 - i) * 86400000).toISOString(),
        ensemble_return: 0.05 + Math.random() * 0.1,
        individual_strategies: {
          'Grid Trading': 0.03 + Math.random() * 0.08,
          'Technical Analysis': 0.04 + Math.random() * 0.06,
          'Trend Following': 0.02 + Math.random() * 0.09,
        },
        drawdown: -Math.random() * 0.05,
        volatility: 0.12 + Math.random() * 0.06,
      })),
      strategy_performance: {
        'Grid Trading': {
          total_return: 0.08,
          max_drawdown: -0.05,
          sharpe_ratio: 1.2,
          win_rate: 0.65,
          profit_factor: 1.5,
          daily_returns: [0.01, -0.005, 0.008, 0.002],
        },
      },
    };
  };

  // Fetch initial ensemble data
  const fetchEnsembleData = useCallback(async () => {
    try {
      setError(null);
      setLoading(true);
      const data = await ensembleAPI.getEnsembleData();
      setEnsembleData(data);
      setLastUpdate(new Date());
    } catch (err: any) {
      console.error('Error fetching ensemble data:', err);
      // Fall back to mock data instead of showing error
      console.log('Falling back to mock data for demo purposes');
      setEnsembleData(generateMockData());
      setLastUpdate(new Date());
      // Don't set error state, just log it
    } finally {
      setLoading(false);
    }
  }, []);

  // WebSocket event handlers
  const handleWeightsUpdate = useCallback((data: EnsembleWeightsEvent) => {
    setEnsembleData(prev => prev ? {
      ...prev,
      strategy_weights: data.strategy_weights.map(w => ({
        ...w,
        last_updated: data.timestamp,
      })),
    } : null);
    setLastUpdate(new Date());
  }, []);

  const handleSignalsUpdate = useCallback((data: EnsembleSignalsEvent) => {
    setEnsembleData(prev => prev ? {
      ...prev,
      current_signals: data.signals.map(s => ({
        ...s,
        timestamp: data.timestamp,
      })),
      signal_conflicts: data.conflicts,
    } : null);
    setLastUpdate(new Date());
  }, []);

  const handlePerformanceUpdate = useCallback((data: EnsemblePerformanceEvent) => {
    setEnsembleData(prev => prev ? {
      ...prev,
      portfolio_metrics: {
        ...prev.portfolio_metrics,
        ...data.portfolio_metrics,
        last_updated: data.timestamp,
      },
    } : null);
    setLastUpdate(new Date());
  }, []);

  const handleRiskUpdate = useCallback((data: EnsembleRiskEvent) => {
    setEnsembleData(prev => prev ? {
      ...prev,
      risk_limits: data.risk_alerts.map(alert => ({
        metric: alert.metric,
        current_value: alert.current_value,
        limit_value: alert.limit_value,
        utilization_pct: (alert.current_value / alert.limit_value) * 100,
        status: alert.status,
      })),
    } : null);
    setLastUpdate(new Date());
  }, []);

  const handleModelUpdate = useCallback((data: EnsembleModelEvent) => {
    setEnsembleData(prev => prev ? {
      ...prev,
      ml_model_status: {
        ...prev.ml_model_status,
        ...data.model_status,
      },
    } : null);
    setLastUpdate(new Date());
  }, []);

  // Initialize component
  useEffect(() => {
    if (!authState.isAuthenticated) return;

    fetchEnsembleData();

    // Set up WebSocket connection for real-time updates
    websocketService.connectEnsemble();
    websocketService.on(WebSocketEventType.ENSEMBLE_WEIGHTS, handleWeightsUpdate);
    websocketService.on(WebSocketEventType.ENSEMBLE_SIGNALS, handleSignalsUpdate);
    websocketService.on(WebSocketEventType.ENSEMBLE_PERFORMANCE, handlePerformanceUpdate);
    websocketService.on(WebSocketEventType.ENSEMBLE_RISK, handleRiskUpdate);
    websocketService.on(WebSocketEventType.ENSEMBLE_MODEL, handleModelUpdate);

    return () => {
      websocketService.off(WebSocketEventType.ENSEMBLE_WEIGHTS, handleWeightsUpdate);
      websocketService.off(WebSocketEventType.ENSEMBLE_SIGNALS, handleSignalsUpdate);
      websocketService.off(WebSocketEventType.ENSEMBLE_PERFORMANCE, handlePerformanceUpdate);
      websocketService.off(WebSocketEventType.ENSEMBLE_RISK, handleRiskUpdate);
      websocketService.off(WebSocketEventType.ENSEMBLE_MODEL, handleModelUpdate);
    };
  }, [authState.isAuthenticated, fetchEnsembleData, handleWeightsUpdate, handleSignalsUpdate, handlePerformanceUpdate, handleRiskUpdate, handleModelUpdate]);

  // Auto-refresh data
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchEnsembleData();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, fetchEnsembleData]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleRefresh = () => {
    fetchEnsembleData();
  };

  const formatPercentage = (value: number) => `${(value * 100).toFixed(2)}%`;
  const formatCurrency = (value: number) => `$${value.toFixed(2)}`;

  if (loading) {
    return (
      <LoadingFallback message="Loading ensemble data..." />
    );
  }

  if (error) {
    return (
      <Alert severity="error" action={
        <IconButton color="inherit" size="small" onClick={handleRefresh}>
          <RefreshIcon />
        </IconButton>
      }>
        {error}
      </Alert>
    );
  }

  if (!ensembleData) {
    return (
      <Alert severity="warning">
        No ensemble data available. Please check your system configuration.
      </Alert>
    );
  }

  // Strategy Weights Component
  const StrategyWeightsPanel = () => {
    const pieData = ensembleData.strategy_weights.map(weight => ({
      name: weight.strategy,
      value: weight.weight,
      confidence: weight.confidence,
      color: STRATEGY_COLORS[weight.strategy as keyof typeof STRATEGY_COLORS] || '#607D8B',
    }));

    return (
      <Card>
        <CardHeader 
          title="Strategy Weights" 
          subheader={`Updated: ${new Date(ensembleData.strategy_weights[0]?.last_updated || '').toLocaleTimeString()}`}
        />
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value }) => `${name}: ${formatPercentage(value)}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <RechartsTooltip 
                formatter={(value: number, name: string) => [formatPercentage(value), name]}
              />
            </PieChart>
          </ResponsiveContainer>
          <Box mt={2}>
            {ensembleData.strategy_weights.map(weight => (
              <Box key={weight.strategy} display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="body2">{weight.strategy}</Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <LinearProgress
                    variant="determinate"
                    value={weight.confidence * 100}
                    sx={{ width: 100, height: 6 }}
                  />
                  <Typography variant="caption">
                    {formatPercentage(weight.confidence)} confidence
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        </CardContent>
      </Card>
    );
  };

  // Signal Monitor Component
  const SignalMonitorPanel = () => {
    return (
      <Card>
        <CardHeader title="Signal Monitor" subheader="Current strategy signals and conflicts" />
        <CardContent>
          <Grid container spacing={2}>
            {ensembleData.current_signals.map(signal => (
              <Grid item xs={12} sm={6} md={4} key={signal.strategy}>
                <Box 
                  p={2} 
                  border={1} 
                  borderColor="grey.300" 
                  borderRadius={1}
                  bgcolor={signal.signal === 'buy' ? 'success.light' : signal.signal === 'sell' ? 'error.light' : 'grey.100'}
                  color={signal.signal === 'buy' ? 'success.contrastText' : signal.signal === 'sell' ? 'error.contrastText' : 'text.primary'}
                >
                  <Typography variant="subtitle2" fontWeight="bold">
                    {signal.strategy}
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1} mt={1}>
                    <Chip
                      label={signal.signal.toUpperCase()}
                      size="small"
                      color={signal.signal === 'buy' ? 'success' : signal.signal === 'sell' ? 'error' : 'default'}
                      icon={signal.signal === 'buy' ? <TrendingUpIcon /> : signal.signal === 'sell' ? <TrendingDownIcon /> : undefined}
                    />
                    <Typography variant="caption">
                      Strength: {formatPercentage(signal.strength)}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={signal.confidence * 100}
                    sx={{ mt: 1, height: 4 }}
                  />
                  <Typography variant="caption" display="block" mt={0.5}>
                    Confidence: {formatPercentage(signal.confidence)}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
          
          {ensembleData.signal_conflicts.length > 0 && (
            <Box mt={3}>
              <Typography variant="h6" gutterBottom color="warning.main">
                Signal Conflicts
              </Typography>
              {ensembleData.signal_conflicts.map((conflict, index) => (
                <Alert 
                  key={index} 
                  severity="warning" 
                  sx={{ mb: 1 }}
                  icon={<WarningIcon />}
                >
                  <Typography variant="body2">
                    Conflicting strategies: {conflict.conflicting_strategies.join(', ')}
                  </Typography>
                  <Typography variant="body2">
                    Resolution: <strong>{conflict.resolution.toUpperCase()}</strong> (Confidence: {formatPercentage(conflict.confidence)})
                  </Typography>
                </Alert>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  // Risk Management Panel
  const RiskManagementPanel = () => {
    return (
      <Card>
        <CardHeader title="Risk Management" subheader="Current risk metrics and limits" />
        <CardContent>
          <Grid container spacing={2}>
            {ensembleData.risk_limits.map(limit => (
              <Grid item xs={12} sm={6} md={4} key={limit.metric}>
                <Box p={2} border={1} borderColor="grey.300" borderRadius={1}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="subtitle2">{limit.metric}</Typography>
                    <Chip
                      size="small"
                      label={limit.status}
                      color={limit.status === 'normal' ? 'success' : limit.status === 'warning' ? 'warning' : 'error'}
                      icon={limit.status === 'normal' ? <CheckCircleIcon /> : limit.status === 'warning' ? <WarningIcon /> : <ErrorIcon />}
                    />
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={Math.min(limit.utilization_pct, 100)}
                    color={limit.status === 'normal' ? 'success' : limit.status === 'warning' ? 'warning' : 'error'}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Box display="flex" justifyContent="space-between" mt={1}>
                    <Typography variant="caption">
                      Current: {limit.current_value.toFixed(2)}
                    </Typography>
                    <Typography variant="caption">
                      Limit: {limit.limit_value.toFixed(2)}
                    </Typography>
                  </Box>
                  <Typography variant="caption" display="block" textAlign="center" mt={0.5}>
                    {limit.utilization_pct.toFixed(1)}% utilized
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  // Performance Metrics Component
  const PerformanceMetricsPanel = () => {
    const performanceData = ensembleData.performance_history.slice(-30); // Last 30 data points

    return (
      <Card>
        <CardHeader title="Portfolio Performance" subheader="Ensemble vs individual strategies" />
        <CardContent>
          <Grid container spacing={3}>
            {/* Key Metrics */}
            <Grid item xs={12}>
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center" p={2} bgcolor="success.light" borderRadius={1}>
                    <Typography variant="h6" color="success.contrastText">
                      {formatPercentage(ensembleData.portfolio_metrics.total_return)}
                    </Typography>
                    <Typography variant="caption" color="success.contrastText">
                      Total Return
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center" p={2} bgcolor="info.light" borderRadius={1}>
                    <Typography variant="h6" color="info.contrastText">
                      {ensembleData.portfolio_metrics.sharpe_ratio.toFixed(2)}
                    </Typography>
                    <Typography variant="caption" color="info.contrastText">
                      Sharpe Ratio
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center" p={2} bgcolor="warning.light" borderRadius={1}>
                    <Typography variant="h6" color="warning.contrastText">
                      {formatPercentage(ensembleData.portfolio_metrics.max_drawdown)}
                    </Typography>
                    <Typography variant="caption" color="warning.contrastText">
                      Max Drawdown
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center" p={2} bgcolor="secondary.light" borderRadius={1}>
                    <Typography variant="h6" color="secondary.contrastText">
                      {formatPercentage(ensembleData.portfolio_metrics.win_rate)}
                    </Typography>
                    <Typography variant="caption" color="secondary.contrastText">
                      Win Rate
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Grid>

            {/* Performance Chart */}
            <Grid item xs={12}>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <YAxis tickFormatter={(value) => formatPercentage(value)} />
                  <RechartsTooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: number) => [formatPercentage(value), 'Return']}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="ensemble_return" 
                    stroke="#2196F3" 
                    strokeWidth={3}
                    name="Ensemble"
                  />
                  {Object.keys(performanceData[0]?.individual_strategies || {}).map((strategy, index) => (
                    <Line
                      key={strategy}
                      type="monotone"
                      dataKey={`individual_strategies.${strategy}`}
                      stroke={Object.values(STRATEGY_COLORS)[index % Object.values(STRATEGY_COLORS).length]}
                      strokeWidth={1}
                      strokeDasharray="5 5"
                      name={strategy}
                    />
                  ))}
                </LineChart>
              </ResponsiveContainer>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  // Correlation Matrix Component
  const CorrelationMatrixPanel = () => {
    const strategies = Array.from(new Set([
      ...ensembleData.strategy_correlations.map(c => c.strategy_a),
      ...ensembleData.strategy_correlations.map(c => c.strategy_b),
    ]));

    const correlationMatrix: Array<{ strategy: string; [key: string]: string | number }> = strategies.map(strategyA => ({
      strategy: strategyA,
      ...strategies.reduce((acc, strategyB) => {
        if (strategyA === strategyB) {
          acc[strategyB] = 1;
        } else {
          const correlation = ensembleData.strategy_correlations.find(
            c => (c.strategy_a === strategyA && c.strategy_b === strategyB) ||
                 (c.strategy_a === strategyB && c.strategy_b === strategyA)
          );
          acc[strategyB] = correlation?.correlation || 0;
        }
        return acc;
      }, {} as Record<string, number>),
    }));

    return (
      <Card>
        <CardHeader title="Strategy Correlation Matrix" subheader="Correlation between different strategies" />
        <CardContent>
          <Box sx={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr>
                  <th style={{ padding: '8px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Strategy</th>
                  {strategies.map(strategy => (
                    <th key={strategy} style={{ padding: '8px', textAlign: 'center', borderBottom: '1px solid #ddd', minWidth: '80px' }}>
                      {strategy.split(' ')[0]}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {correlationMatrix.map(row => (
                  <tr key={row.strategy}>
                    <td style={{ padding: '8px', fontWeight: 'bold', borderRight: '1px solid #ddd' }}>
                      {row.strategy}
                    </td>
                    {strategies.map(strategy => {
                      const correlation = row[strategy] as number;
                      const intensity = Math.abs(correlation);
                      const color = correlation > 0 ? `rgba(76, 175, 80, ${intensity})` : `rgba(244, 67, 54, ${intensity})`;
                      
                      return (
                        <td 
                          key={strategy}
                          style={{ 
                            padding: '8px', 
                            textAlign: 'center',
                            backgroundColor: color,
                            border: '1px solid #ddd',
                          }}
                        >
                          {correlation.toFixed(2)}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </Box>
        </CardContent>
      </Card>
    );
  };

  // ML Model Status Component
  const MLModelStatusPanel = () => {
    return (
      <Card>
        <CardHeader title="ML Model Status" subheader="Current model performance and health" />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="h6" gutterBottom>Model Information</Typography>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Model Name:</Typography>
                  <Typography variant="body2" fontWeight="bold">{ensembleData.ml_model_status.model_name}</Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Version:</Typography>
                  <Typography variant="body2" fontWeight="bold">{ensembleData.ml_model_status.version}</Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Status:</Typography>
                  <Chip
                    size="small"
                    label={ensembleData.ml_model_status.status}
                    color={ensembleData.ml_model_status.status === 'active' ? 'success' : ensembleData.ml_model_status.status === 'training' ? 'warning' : 'error'}
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Last Trained:</Typography>
                  <Typography variant="body2">{new Date(ensembleData.ml_model_status.last_trained).toLocaleString()}</Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Next Training:</Typography>
                  <Typography variant="body2">{new Date(ensembleData.ml_model_status.next_training).toLocaleString()}</Typography>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="h6" gutterBottom>Performance Metrics</Typography>
                <Box mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2">Accuracy</Typography>
                    <Typography variant="body2" fontWeight="bold">{formatPercentage(ensembleData.ml_model_status.accuracy)}</Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={ensembleData.ml_model_status.accuracy * 100} 
                    color="success"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
                
                <Box mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2">Confidence</Typography>
                    <Typography variant="body2" fontWeight="bold">{formatPercentage(ensembleData.ml_model_status.confidence)}</Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={ensembleData.ml_model_status.confidence * 100} 
                    color="info"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>

                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Features:</Typography>
                  <Typography variant="body2" fontWeight="bold">{ensembleData.ml_model_status.feature_count}</Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Training Samples:</Typography>
                  <Typography variant="body2" fontWeight="bold">{ensembleData.ml_model_status.training_samples.toLocaleString()}</Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  return (
    <DashboardLayout>
      <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Strategy Ensemble Dashboard
        </Typography>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="caption" color="text.secondary">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  size="small"
                />
              }
              label="Auto-refresh"
            />
            <Tooltip title="Refresh data">
              <IconButton onClick={handleRefresh} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="ensemble dashboard tabs">
          <Tab label="Overview" />
          <Tab label="Performance" />
          <Tab label="Correlations" />
          <Tab label="ML Model" />
        </Tabs>
      </Box>

      {/* Tab Panels */}
      <TabPanel value={tabValue} index={0}>
        {/* Overview Tab */}
        <ErrorBoundary>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={4}>
              <ErrorBoundary>
                <StrategyWeightsPanel />
              </ErrorBoundary>
            </Grid>
            <Grid item xs={12} lg={8}>
              <ErrorBoundary>
                <SignalMonitorPanel />
              </ErrorBoundary>
            </Grid>
            <Grid item xs={12}>
              <ErrorBoundary>
                <RiskManagementPanel />
              </ErrorBoundary>
            </Grid>
          </Grid>
        </ErrorBoundary>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Performance Tab */}
        <ErrorBoundary>
          <PerformanceMetricsPanel />
        </ErrorBoundary>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Correlations Tab */}
        <ErrorBoundary>
          <CorrelationMatrixPanel />
        </ErrorBoundary>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        {/* ML Model Tab */}
        <ErrorBoundary>
          <MLModelStatusPanel />
        </ErrorBoundary>
      </TabPanel>
      </Box>
    </DashboardLayout>
  );
};

export default StrategyEnsemble;