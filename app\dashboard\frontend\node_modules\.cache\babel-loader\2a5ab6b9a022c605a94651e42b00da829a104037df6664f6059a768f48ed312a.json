{"ast": null, "code": "export { default } from \"./createStyled.js\";\nexport * from \"./createStyled.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/system/esm/createStyled/index.js"], "sourcesContent": ["export { default } from \"./createStyled.js\";\nexport * from \"./createStyled.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}