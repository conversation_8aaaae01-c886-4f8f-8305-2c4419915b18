{"ast": null, "code": "import deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from \"../createBreakpoints/createBreakpoints.js\";\nimport cssContainerQueries from \"../cssContainerQueries/index.js\";\nimport shape from \"./shape.js\";\nimport createSpacing from \"./createSpacing.js\";\nimport styleFunctionSx from \"../styleFunctionSx/styleFunctionSx.js\";\nimport defaultSxConfig from \"../styleFunctionSx/defaultSxConfig.js\";\nimport applyStyles from \"./applyStyles.js\";\nfunction createTheme() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    breakpoints: breakpointsInput = {},\n    palette: paletteInput = {},\n    spacing: spacingInput,\n    shape: shapeInput = {},\n    ...other\n  } = options;\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: {\n      mode: 'light',\n      ...paletteInput\n    },\n    spacing,\n    shape: {\n      ...shape,\n      ...shapeInput\n    }\n  }, other);\n  muiTheme = cssContainerQueries(muiTheme);\n  muiTheme.applyStyles = applyStyles;\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;", "map": {"version": 3, "names": ["deepmerge", "createBreakpoints", "cssContainerQueries", "shape", "createSpacing", "styleFunctionSx", "defaultSxConfig", "applyStyles", "createTheme", "options", "arguments", "length", "undefined", "breakpoints", "breakpointsInput", "palette", "paletteInput", "spacing", "spacingInput", "shapeInput", "other", "muiTheme", "direction", "components", "mode", "_len", "args", "Array", "_key", "reduce", "acc", "argument", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/system/esm/createTheme/createTheme.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from \"../createBreakpoints/createBreakpoints.js\";\nimport cssContainerQueries from \"../cssContainerQueries/index.js\";\nimport shape from \"./shape.js\";\nimport createSpacing from \"./createSpacing.js\";\nimport styleFunctionSx from \"../styleFunctionSx/styleFunctionSx.js\";\nimport defaultSxConfig from \"../styleFunctionSx/defaultSxConfig.js\";\nimport applyStyles from \"./applyStyles.js\";\nfunction createTheme(options = {}, ...args) {\n  const {\n    breakpoints: breakpointsInput = {},\n    palette: paletteInput = {},\n    spacing: spacingInput,\n    shape: shapeInput = {},\n    ...other\n  } = options;\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: {\n      mode: 'light',\n      ...paletteInput\n    },\n    spacing,\n    shape: {\n      ...shape,\n      ...shapeInput\n    }\n  }, other);\n  muiTheme = cssContainerQueries(muiTheme);\n  muiTheme.applyStyles = applyStyles;\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,iBAAiB,MAAM,2CAA2C;AACzE,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,WAAWA,CAAA,EAAwB;EAAA,IAAvBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC/B,MAAM;IACJG,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;IAClCC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;IAC1BC,OAAO,EAAEC,YAAY;IACrBf,KAAK,EAAEgB,UAAU,GAAG,CAAC,CAAC;IACtB,GAAGC;EACL,CAAC,GAAGX,OAAO;EACX,MAAMI,WAAW,GAAGZ,iBAAiB,CAACa,gBAAgB,CAAC;EACvD,MAAMG,OAAO,GAAGb,aAAa,CAACc,YAAY,CAAC;EAC3C,IAAIG,QAAQ,GAAGrB,SAAS,CAAC;IACvBa,WAAW;IACXS,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,CAAC,CAAC;IACd;IACAR,OAAO,EAAE;MACPS,IAAI,EAAE,OAAO;MACb,GAAGR;IACL,CAAC;IACDC,OAAO;IACPd,KAAK,EAAE;MACL,GAAGA,KAAK;MACR,GAAGgB;IACL;EACF,CAAC,EAAEC,KAAK,CAAC;EACTC,QAAQ,GAAGnB,mBAAmB,CAACmB,QAAQ,CAAC;EACxCA,QAAQ,CAACd,WAAW,GAAGA,WAAW;EAAC,SAAAkB,IAAA,GAAAf,SAAA,CAAAC,MAAA,EA1BCe,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAlB,SAAA,CAAAkB,IAAA;EAAA;EA2BxCP,QAAQ,GAAGK,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAK/B,SAAS,CAAC8B,GAAG,EAAEC,QAAQ,CAAC,EAAEV,QAAQ,CAAC;EAC7EA,QAAQ,CAACW,iBAAiB,GAAG;IAC3B,GAAG1B,eAAe;IAClB,GAAGc,KAAK,EAAEY;EACZ,CAAC;EACDX,QAAQ,CAACY,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAO9B,eAAe,CAAC;MACrB6B,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACD,OAAOf,QAAQ;AACjB;AACA,eAAeb,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}