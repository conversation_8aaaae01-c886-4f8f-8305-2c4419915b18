{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosURLSearchParams from '../helpers/AxiosURLSearchParams.js';\n\n/**\n * It replaces all instances of the characters `:`, `$`, `,`, `+`, `[`, and `]` with their\n * URI encoded counterparts\n *\n * @param {string} val The value to be encoded.\n *\n * @returns {string} The encoded value.\n */\nfunction encode(val) {\n  return encodeURIComponent(val).replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',').replace(/%20/g, '+').replace(/%5B/gi, '[').replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @param {?(object|Function)} options\n *\n * @returns {string} The formatted url\n */\nexport default function buildURL(url, params, options) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n  const _encode = options && options.encode || encode;\n  if (utils.isFunction(options)) {\n    options = {\n      serialize: options\n    };\n  }\n  const serializeFn = options && options.serialize;\n  let serializedParams;\n  if (serializeFn) {\n    serializedParams = serializeFn(params, options);\n  } else {\n    serializedParams = utils.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);\n  }\n  if (serializedParams) {\n    const hashmarkIndex = url.indexOf(\"#\");\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n  return url;\n}", "map": {"version": 3, "names": ["utils", "AxiosURLSearchParams", "encode", "val", "encodeURIComponent", "replace", "buildURL", "url", "params", "options", "_encode", "isFunction", "serialize", "serializeFn", "serializedParams", "isURLSearchParams", "toString", "hashmarkIndex", "indexOf", "slice"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/axios/lib/helpers/buildURL.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosURLSearchParams from '../helpers/AxiosURLSearchParams.js';\n\n/**\n * It replaces all instances of the characters `:`, `$`, `,`, `+`, `[`, and `]` with their\n * URI encoded counterparts\n *\n * @param {string} val The value to be encoded.\n *\n * @returns {string} The encoded value.\n */\nfunction encode(val) {\n  return encodeURIComponent(val).\n    replace(/%3A/gi, ':').\n    replace(/%24/g, '$').\n    replace(/%2C/gi, ',').\n    replace(/%20/g, '+').\n    replace(/%5B/gi, '[').\n    replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @param {?(object|Function)} options\n *\n * @returns {string} The formatted url\n */\nexport default function buildURL(url, params, options) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n  \n  const _encode = options && options.encode || encode;\n\n  if (utils.isFunction(options)) {\n    options = {\n      serialize: options\n    };\n  } \n\n  const serializeFn = options && options.serialize;\n\n  let serializedParams;\n\n  if (serializeFn) {\n    serializedParams = serializeFn(params, options);\n  } else {\n    serializedParams = utils.isURLSearchParams(params) ?\n      params.toString() :\n      new AxiosURLSearchParams(params, options).toString(_encode);\n  }\n\n  if (serializedParams) {\n    const hashmarkIndex = url.indexOf(\"#\");\n\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,oBAAoB,MAAM,oCAAoC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,GAAG,EAAE;EACnB,OAAOC,kBAAkB,CAACD,GAAG,CAAC,CAC5BE,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD;EACA,IAAI,CAACD,MAAM,EAAE;IACX,OAAOD,GAAG;EACZ;EAEA,MAAMG,OAAO,GAAGD,OAAO,IAAIA,OAAO,CAACP,MAAM,IAAIA,MAAM;EAEnD,IAAIF,KAAK,CAACW,UAAU,CAACF,OAAO,CAAC,EAAE;IAC7BA,OAAO,GAAG;MACRG,SAAS,EAAEH;IACb,CAAC;EACH;EAEA,MAAMI,WAAW,GAAGJ,OAAO,IAAIA,OAAO,CAACG,SAAS;EAEhD,IAAIE,gBAAgB;EAEpB,IAAID,WAAW,EAAE;IACfC,gBAAgB,GAAGD,WAAW,CAACL,MAAM,EAAEC,OAAO,CAAC;EACjD,CAAC,MAAM;IACLK,gBAAgB,GAAGd,KAAK,CAACe,iBAAiB,CAACP,MAAM,CAAC,GAChDA,MAAM,CAACQ,QAAQ,CAAC,CAAC,GACjB,IAAIf,oBAAoB,CAACO,MAAM,EAAEC,OAAO,CAAC,CAACO,QAAQ,CAACN,OAAO,CAAC;EAC/D;EAEA,IAAII,gBAAgB,EAAE;IACpB,MAAMG,aAAa,GAAGV,GAAG,CAACW,OAAO,CAAC,GAAG,CAAC;IAEtC,IAAID,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBV,GAAG,GAAGA,GAAG,CAACY,KAAK,CAAC,CAAC,EAAEF,aAAa,CAAC;IACnC;IACAV,GAAG,IAAI,CAACA,GAAG,CAACW,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,IAAIJ,gBAAgB;EACjE;EAEA,OAAOP,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}