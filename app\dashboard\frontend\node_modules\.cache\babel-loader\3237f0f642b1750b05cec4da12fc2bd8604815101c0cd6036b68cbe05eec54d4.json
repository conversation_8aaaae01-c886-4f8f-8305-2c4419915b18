{"ast": null, "code": "'use client';\n\nvar _span;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NotchedOutlineRoot = styled('fieldset', {\n  shouldForwardProp: rootShouldForwardProp\n})({\n  textAlign: 'left',\n  position: 'absolute',\n  bottom: 0,\n  right: 0,\n  top: -5,\n  left: 0,\n  margin: 0,\n  padding: '0 8px',\n  pointerEvents: 'none',\n  borderRadius: 'inherit',\n  borderStyle: 'solid',\n  borderWidth: 1,\n  overflow: 'hidden',\n  minWidth: '0%'\n});\nconst NotchedOutlineLegend = styled('legend', {\n  shouldForwardProp: rootShouldForwardProp\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    float: 'unset',\n    // Fix conflict with bootstrap\n    width: 'auto',\n    // Fix conflict with bootstrap\n    overflow: 'hidden',\n    // Fix Horizontal scroll when label too long\n    variants: [{\n      props: _ref2 => {\n        let {\n          ownerState\n        } = _ref2;\n        return !ownerState.withLabel;\n      },\n      style: {\n        padding: 0,\n        lineHeight: '11px',\n        // sync with `height` in `legend` styles\n        transition: theme.transitions.create('width', {\n          duration: 150,\n          easing: theme.transitions.easing.easeOut\n        })\n      }\n    }, {\n      props: _ref3 => {\n        let {\n          ownerState\n        } = _ref3;\n        return ownerState.withLabel;\n      },\n      style: {\n        display: 'block',\n        // Fix conflict with normalize.css and sanitize.css\n        padding: 0,\n        height: 11,\n        // sync with `lineHeight` in `legend` styles\n        fontSize: '0.75em',\n        visibility: 'hidden',\n        maxWidth: 0.01,\n        transition: theme.transitions.create('max-width', {\n          duration: 50,\n          easing: theme.transitions.easing.easeOut\n        }),\n        whiteSpace: 'nowrap',\n        '& > span': {\n          paddingLeft: 5,\n          paddingRight: 5,\n          display: 'inline-block',\n          opacity: 0,\n          visibility: 'visible'\n        }\n      }\n    }, {\n      props: _ref4 => {\n        let {\n          ownerState\n        } = _ref4;\n        return ownerState.withLabel && ownerState.notched;\n      },\n      style: {\n        maxWidth: '100%',\n        transition: theme.transitions.create('max-width', {\n          duration: 100,\n          easing: theme.transitions.easing.easeOut,\n          delay: 50\n        })\n      }\n    }]\n  };\n}));\n\n/**\n * @ignore - internal component.\n */\nexport default function NotchedOutline(props) {\n  const {\n    children,\n    classes,\n    className,\n    label,\n    notched,\n    ...other\n  } = props;\n  const withLabel = label != null && label !== '';\n  const ownerState = {\n    ...props,\n    notched,\n    withLabel\n  };\n  return /*#__PURE__*/_jsx(NotchedOutlineRoot, {\n    \"aria-hidden\": true,\n    className: className,\n    ownerState: ownerState,\n    ...other,\n    children: /*#__PURE__*/_jsx(NotchedOutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(\"span\", {\n        children: label\n      }) :\n      // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        \"aria-hidden\": true,\n        children: \"\\u200B\"\n      }))\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? NotchedOutline.propTypes /* remove-proptypes */ = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The label.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, the outline is notched to accommodate the label.\n   */\n  notched: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object\n} : void 0;", "map": {"version": 3, "names": ["_span", "React", "PropTypes", "rootShouldForwardProp", "styled", "memoTheme", "jsx", "_jsx", "NotchedOutlineRoot", "shouldForwardProp", "textAlign", "position", "bottom", "right", "top", "left", "margin", "padding", "pointerEvents", "borderRadius", "borderStyle", "borderWidth", "overflow", "min<PERSON><PERSON><PERSON>", "NotchedOutlineLegend", "_ref", "theme", "float", "width", "variants", "props", "_ref2", "ownerState", "<PERSON><PERSON><PERSON><PERSON>", "style", "lineHeight", "transition", "transitions", "create", "duration", "easing", "easeOut", "_ref3", "display", "height", "fontSize", "visibility", "max<PERSON><PERSON><PERSON>", "whiteSpace", "paddingLeft", "paddingRight", "opacity", "_ref4", "notched", "delay", "NotchedOutline", "children", "classes", "className", "label", "other", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "bool", "isRequired"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/OutlinedInput/NotchedOutline.js"], "sourcesContent": ["'use client';\n\nvar _span;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NotchedOutlineRoot = styled('fieldset', {\n  shouldForwardProp: rootShouldForwardProp\n})({\n  textAlign: 'left',\n  position: 'absolute',\n  bottom: 0,\n  right: 0,\n  top: -5,\n  left: 0,\n  margin: 0,\n  padding: '0 8px',\n  pointerEvents: 'none',\n  borderRadius: 'inherit',\n  borderStyle: 'solid',\n  borderWidth: 1,\n  overflow: 'hidden',\n  minWidth: '0%'\n});\nconst NotchedOutlineLegend = styled('legend', {\n  shouldForwardProp: rootShouldForwardProp\n})(memoTheme(({\n  theme\n}) => ({\n  float: 'unset',\n  // Fix conflict with bootstrap\n  width: 'auto',\n  // Fix conflict with bootstrap\n  overflow: 'hidden',\n  // Fix Horizontal scroll when label too long\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.withLabel,\n    style: {\n      padding: 0,\n      lineHeight: '11px',\n      // sync with `height` in `legend` styles\n      transition: theme.transitions.create('width', {\n        duration: 150,\n        easing: theme.transitions.easing.easeOut\n      })\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.withLabel,\n    style: {\n      display: 'block',\n      // Fix conflict with normalize.css and sanitize.css\n      padding: 0,\n      height: 11,\n      // sync with `lineHeight` in `legend` styles\n      fontSize: '0.75em',\n      visibility: 'hidden',\n      maxWidth: 0.01,\n      transition: theme.transitions.create('max-width', {\n        duration: 50,\n        easing: theme.transitions.easing.easeOut\n      }),\n      whiteSpace: 'nowrap',\n      '& > span': {\n        paddingLeft: 5,\n        paddingRight: 5,\n        display: 'inline-block',\n        opacity: 0,\n        visibility: 'visible'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.withLabel && ownerState.notched,\n    style: {\n      maxWidth: '100%',\n      transition: theme.transitions.create('max-width', {\n        duration: 100,\n        easing: theme.transitions.easing.easeOut,\n        delay: 50\n      })\n    }\n  }]\n})));\n\n/**\n * @ignore - internal component.\n */\nexport default function NotchedOutline(props) {\n  const {\n    children,\n    classes,\n    className,\n    label,\n    notched,\n    ...other\n  } = props;\n  const withLabel = label != null && label !== '';\n  const ownerState = {\n    ...props,\n    notched,\n    withLabel\n  };\n  return /*#__PURE__*/_jsx(NotchedOutlineRoot, {\n    \"aria-hidden\": true,\n    className: className,\n    ownerState: ownerState,\n    ...other,\n    children: /*#__PURE__*/_jsx(NotchedOutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(\"span\", {\n        children: label\n      }) : // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        \"aria-hidden\": true,\n        children: \"\\u200B\"\n      }))\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? NotchedOutline.propTypes /* remove-proptypes */ = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The label.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, the outline is notched to accommodate the label.\n   */\n  notched: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK;AACT,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGJ,MAAM,CAAC,UAAU,EAAE;EAC5CK,iBAAiB,EAAEN;AACrB,CAAC,CAAC,CAAC;EACDO,SAAS,EAAE,MAAM;EACjBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,OAAO;EAChBC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,OAAO;EACpBC,WAAW,EAAE,CAAC;EACdC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAGpB,MAAM,CAAC,QAAQ,EAAE;EAC5CK,iBAAiB,EAAEN;AACrB,CAAC,CAAC,CAACE,SAAS,CAACoB,IAAA;EAAA,IAAC;IACZC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,KAAK,EAAE,OAAO;IACd;IACAC,KAAK,EAAE,MAAM;IACb;IACAN,QAAQ,EAAE,QAAQ;IAClB;IACAO,QAAQ,EAAE,CAAC;MACTC,KAAK,EAAEC,KAAA;QAAA,IAAC;UACNC;QACF,CAAC,GAAAD,KAAA;QAAA,OAAK,CAACC,UAAU,CAACC,SAAS;MAAA;MAC3BC,KAAK,EAAE;QACLjB,OAAO,EAAE,CAAC;QACVkB,UAAU,EAAE,MAAM;QAClB;QACAC,UAAU,EAAEV,KAAK,CAACW,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;UAC5CC,QAAQ,EAAE,GAAG;UACbC,MAAM,EAAEd,KAAK,CAACW,WAAW,CAACG,MAAM,CAACC;QACnC,CAAC;MACH;IACF,CAAC,EAAE;MACDX,KAAK,EAAEY,KAAA;QAAA,IAAC;UACNV;QACF,CAAC,GAAAU,KAAA;QAAA,OAAKV,UAAU,CAACC,SAAS;MAAA;MAC1BC,KAAK,EAAE;QACLS,OAAO,EAAE,OAAO;QAChB;QACA1B,OAAO,EAAE,CAAC;QACV2B,MAAM,EAAE,EAAE;QACV;QACAC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,IAAI;QACdX,UAAU,EAAEV,KAAK,CAACW,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;UAChDC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEd,KAAK,CAACW,WAAW,CAACG,MAAM,CAACC;QACnC,CAAC,CAAC;QACFO,UAAU,EAAE,QAAQ;QACpB,UAAU,EAAE;UACVC,WAAW,EAAE,CAAC;UACdC,YAAY,EAAE,CAAC;UACfP,OAAO,EAAE,cAAc;UACvBQ,OAAO,EAAE,CAAC;UACVL,UAAU,EAAE;QACd;MACF;IACF,CAAC,EAAE;MACDhB,KAAK,EAAEsB,KAAA;QAAA,IAAC;UACNpB;QACF,CAAC,GAAAoB,KAAA;QAAA,OAAKpB,UAAU,CAACC,SAAS,IAAID,UAAU,CAACqB,OAAO;MAAA;MAChDnB,KAAK,EAAE;QACLa,QAAQ,EAAE,MAAM;QAChBX,UAAU,EAAEV,KAAK,CAACW,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;UAChDC,QAAQ,EAAE,GAAG;UACbC,MAAM,EAAEd,KAAK,CAACW,WAAW,CAACG,MAAM,CAACC,OAAO;UACxCa,KAAK,EAAE;QACT,CAAC;MACH;IACF,CAAC;EACH,CAAC;AAAA,CAAC,CAAC,CAAC;;AAEJ;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACzB,KAAK,EAAE;EAC5C,MAAM;IACJ0B,QAAQ;IACRC,OAAO;IACPC,SAAS;IACTC,KAAK;IACLN,OAAO;IACP,GAAGO;EACL,CAAC,GAAG9B,KAAK;EACT,MAAMG,SAAS,GAAG0B,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,EAAE;EAC/C,MAAM3B,UAAU,GAAG;IACjB,GAAGF,KAAK;IACRuB,OAAO;IACPpB;EACF,CAAC;EACD,OAAO,aAAa1B,IAAI,CAACC,kBAAkB,EAAE;IAC3C,aAAa,EAAE,IAAI;IACnBkD,SAAS,EAAEA,SAAS;IACpB1B,UAAU,EAAEA,UAAU;IACtB,GAAG4B,KAAK;IACRJ,QAAQ,EAAE,aAAajD,IAAI,CAACiB,oBAAoB,EAAE;MAChDQ,UAAU,EAAEA,UAAU;MACtBwB,QAAQ,EAAEvB,SAAS,GAAG,aAAa1B,IAAI,CAAC,MAAM,EAAE;QAC9CiD,QAAQ,EAAEG;MACZ,CAAC,CAAC;MAAG;MACL3D,KAAK,KAAKA,KAAK,GAAG,aAAaO,IAAI,CAAC,MAAM,EAAE;QAC1CmD,SAAS,EAAE,aAAa;QACxB,aAAa,EAAE,IAAI;QACnBF,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ;AACAK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,cAAc,CAACS,SAAS,CAAC,yBAAyB;EACxF;AACF;AACA;EACER,QAAQ,EAAEtD,SAAS,CAAC+D,IAAI;EACxB;AACF;AACA;EACER,OAAO,EAAEvD,SAAS,CAACgE,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAExD,SAAS,CAACiE,MAAM;EAC3B;AACF;AACA;EACER,KAAK,EAAEzD,SAAS,CAAC+D,IAAI;EACrB;AACF;AACA;EACEZ,OAAO,EAAEnD,SAAS,CAACkE,IAAI,CAACC,UAAU;EAClC;AACF;AACA;EACEnC,KAAK,EAAEhC,SAAS,CAACgE;AACnB,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}