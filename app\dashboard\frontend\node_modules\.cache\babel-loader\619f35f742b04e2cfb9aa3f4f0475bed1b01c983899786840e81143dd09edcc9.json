{"ast": null, "code": "import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n/**\n * For using in `sx` prop to sort the breakpoint from low to high.\n * Note: this function does not work and will not support multiple units.\n *       e.g. input: { '@container (min-width:300px)': '1rem', '@container (min-width:40rem)': '2rem' }\n *            output: { '@container (min-width:40rem)': '2rem', '@container (min-width:300px)': '1rem' } // since 40 < 300 eventhough 40rem > 300px\n */\nexport function sortContainerQueries(theme, css) {\n  if (!theme.containerQueries) {\n    return css;\n  }\n  const sorted = Object.keys(css).filter(key => key.startsWith('@container')).sort((a, b) => {\n    const regex = /min-width:\\s*([0-9.]+)/;\n    return +(a.match(regex)?.[1] || 0) - +(b.match(regex)?.[1] || 0);\n  });\n  if (!sorted.length) {\n    return css;\n  }\n  return sorted.reduce((acc, key) => {\n    const value = css[key];\n    delete acc[key];\n    acc[key] = value;\n    return acc;\n  }, {\n    ...css\n  });\n}\nexport function isCqShorthand(breakpointKeys, value) {\n  return value === '@' || value.startsWith('@') && (breakpointKeys.some(key => value.startsWith(`@${key}`)) || !!value.match(/^@\\d/));\n}\nexport function getContainerQuery(theme, shorthand) {\n  const matches = shorthand.match(/^@([^/]+)?\\/?(.+)?$/);\n  if (!matches) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The provided shorthand ${`(${shorthand})`} is invalid. The format should be \\`@<breakpoint | number>\\` or \\`@<breakpoint | number>/<container>\\`.\\n` + 'For example, `@sm` or `@600` or `@40rem/sidebar`.' : _formatMuiErrorMessage(18, `(${shorthand})`));\n    }\n    return null;\n  }\n  const [, containerQuery, containerName] = matches;\n  const value = Number.isNaN(+containerQuery) ? containerQuery || 0 : +containerQuery;\n  return theme.containerQueries(containerName).up(value);\n}\nexport default function cssContainerQueries(themeInput) {\n  const toContainerQuery = (mediaQuery, name) => mediaQuery.replace('@media', name ? `@container ${name}` : '@container');\n  function attachCq(node, name) {\n    node.up = function () {\n      return toContainerQuery(themeInput.breakpoints.up(...arguments), name);\n    };\n    node.down = function () {\n      return toContainerQuery(themeInput.breakpoints.down(...arguments), name);\n    };\n    node.between = function () {\n      return toContainerQuery(themeInput.breakpoints.between(...arguments), name);\n    };\n    node.only = function () {\n      return toContainerQuery(themeInput.breakpoints.only(...arguments), name);\n    };\n    node.not = function () {\n      const result = toContainerQuery(themeInput.breakpoints.not(...arguments), name);\n      if (result.includes('not all and')) {\n        // `@container` does not work with `not all and`, so need to invert the logic\n        return result.replace('not all and ', '').replace('min-width:', 'width<').replace('max-width:', 'width>').replace('and', 'or');\n      }\n      return result;\n    };\n  }\n  const node = {};\n  const containerQueries = name => {\n    attachCq(node, name);\n    return node;\n  };\n  attachCq(containerQueries);\n  return {\n    ...themeInput,\n    containerQueries\n  };\n}", "map": {"version": 3, "names": ["_formatMuiErrorMessage", "sortContainerQueries", "theme", "css", "containerQueries", "sorted", "Object", "keys", "filter", "key", "startsWith", "sort", "a", "b", "regex", "match", "length", "reduce", "acc", "value", "isCqShorthand", "breakpoint<PERSON><PERSON><PERSON>", "some", "getC<PERSON><PERSON><PERSON><PERSON><PERSON>", "shorthand", "matches", "process", "env", "NODE_ENV", "Error", "containerQuery", "containerName", "Number", "isNaN", "up", "cssContainerQueries", "themeInput", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mediaQuery", "name", "replace", "attachCq", "node", "breakpoints", "arguments", "down", "between", "only", "not", "result", "includes"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/system/esm/cssContainerQueries/cssContainerQueries.js"], "sourcesContent": ["import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n/**\n * For using in `sx` prop to sort the breakpoint from low to high.\n * Note: this function does not work and will not support multiple units.\n *       e.g. input: { '@container (min-width:300px)': '1rem', '@container (min-width:40rem)': '2rem' }\n *            output: { '@container (min-width:40rem)': '2rem', '@container (min-width:300px)': '1rem' } // since 40 < 300 eventhough 40rem > 300px\n */\nexport function sortContainerQueries(theme, css) {\n  if (!theme.containerQueries) {\n    return css;\n  }\n  const sorted = Object.keys(css).filter(key => key.startsWith('@container')).sort((a, b) => {\n    const regex = /min-width:\\s*([0-9.]+)/;\n    return +(a.match(regex)?.[1] || 0) - +(b.match(regex)?.[1] || 0);\n  });\n  if (!sorted.length) {\n    return css;\n  }\n  return sorted.reduce((acc, key) => {\n    const value = css[key];\n    delete acc[key];\n    acc[key] = value;\n    return acc;\n  }, {\n    ...css\n  });\n}\nexport function isCqShorthand(breakpointKeys, value) {\n  return value === '@' || value.startsWith('@') && (breakpointKeys.some(key => value.startsWith(`@${key}`)) || !!value.match(/^@\\d/));\n}\nexport function getContainerQuery(theme, shorthand) {\n  const matches = shorthand.match(/^@([^/]+)?\\/?(.+)?$/);\n  if (!matches) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The provided shorthand ${`(${shorthand})`} is invalid. The format should be \\`@<breakpoint | number>\\` or \\`@<breakpoint | number>/<container>\\`.\\n` + 'For example, `@sm` or `@600` or `@40rem/sidebar`.' : _formatMuiErrorMessage(18, `(${shorthand})`));\n    }\n    return null;\n  }\n  const [, containerQuery, containerName] = matches;\n  const value = Number.isNaN(+containerQuery) ? containerQuery || 0 : +containerQuery;\n  return theme.containerQueries(containerName).up(value);\n}\nexport default function cssContainerQueries(themeInput) {\n  const toContainerQuery = (mediaQuery, name) => mediaQuery.replace('@media', name ? `@container ${name}` : '@container');\n  function attachCq(node, name) {\n    node.up = (...args) => toContainerQuery(themeInput.breakpoints.up(...args), name);\n    node.down = (...args) => toContainerQuery(themeInput.breakpoints.down(...args), name);\n    node.between = (...args) => toContainerQuery(themeInput.breakpoints.between(...args), name);\n    node.only = (...args) => toContainerQuery(themeInput.breakpoints.only(...args), name);\n    node.not = (...args) => {\n      const result = toContainerQuery(themeInput.breakpoints.not(...args), name);\n      if (result.includes('not all and')) {\n        // `@container` does not work with `not all and`, so need to invert the logic\n        return result.replace('not all and ', '').replace('min-width:', 'width<').replace('max-width:', 'width>').replace('and', 'or');\n      }\n      return result;\n    };\n  }\n  const node = {};\n  const containerQueries = name => {\n    attachCq(node, name);\n    return node;\n  };\n  attachCq(containerQueries);\n  return {\n    ...themeInput,\n    containerQueries\n  };\n}"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,kCAAkC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/C,IAAI,CAACD,KAAK,CAACE,gBAAgB,EAAE;IAC3B,OAAOD,GAAG;EACZ;EACA,MAAME,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACJ,GAAG,CAAC,CAACK,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACzF,MAAMC,KAAK,GAAG,wBAAwB;IACtC,OAAO,EAAEF,CAAC,CAACG,KAAK,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAED,CAAC,CAACE,KAAK,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;EAClE,CAAC,CAAC;EACF,IAAI,CAACT,MAAM,CAACW,MAAM,EAAE;IAClB,OAAOb,GAAG;EACZ;EACA,OAAOE,MAAM,CAACY,MAAM,CAAC,CAACC,GAAG,EAAET,GAAG,KAAK;IACjC,MAAMU,KAAK,GAAGhB,GAAG,CAACM,GAAG,CAAC;IACtB,OAAOS,GAAG,CAACT,GAAG,CAAC;IACfS,GAAG,CAACT,GAAG,CAAC,GAAGU,KAAK;IAChB,OAAOD,GAAG;EACZ,CAAC,EAAE;IACD,GAAGf;EACL,CAAC,CAAC;AACJ;AACA,OAAO,SAASiB,aAAaA,CAACC,cAAc,EAAEF,KAAK,EAAE;EACnD,OAAOA,KAAK,KAAK,GAAG,IAAIA,KAAK,CAACT,UAAU,CAAC,GAAG,CAAC,KAAKW,cAAc,CAACC,IAAI,CAACb,GAAG,IAAIU,KAAK,CAACT,UAAU,CAAC,IAAID,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAACU,KAAK,CAACJ,KAAK,CAAC,MAAM,CAAC,CAAC;AACrI;AACA,OAAO,SAASQ,iBAAiBA,CAACrB,KAAK,EAAEsB,SAAS,EAAE;EAClD,MAAMC,OAAO,GAAGD,SAAS,CAACT,KAAK,CAAC,qBAAqB,CAAC;EACtD,IAAI,CAACU,OAAO,EAAE;IACZ,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAM,IAAIC,KAAK,CAACH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,+BAA+B,IAAIJ,SAAS,GAAG,2GAA2G,GAAG,mDAAmD,GAAGxB,sBAAsB,CAAC,EAAE,EAAE,IAAIwB,SAAS,GAAG,CAAC,CAAC;IAC1T;IACA,OAAO,IAAI;EACb;EACA,MAAM,GAAGM,cAAc,EAAEC,aAAa,CAAC,GAAGN,OAAO;EACjD,MAAMN,KAAK,GAAGa,MAAM,CAACC,KAAK,CAAC,CAACH,cAAc,CAAC,GAAGA,cAAc,IAAI,CAAC,GAAG,CAACA,cAAc;EACnF,OAAO5B,KAAK,CAACE,gBAAgB,CAAC2B,aAAa,CAAC,CAACG,EAAE,CAACf,KAAK,CAAC;AACxD;AACA,eAAe,SAASgB,mBAAmBA,CAACC,UAAU,EAAE;EACtD,MAAMC,gBAAgB,GAAGA,CAACC,UAAU,EAAEC,IAAI,KAAKD,UAAU,CAACE,OAAO,CAAC,QAAQ,EAAED,IAAI,GAAG,cAAcA,IAAI,EAAE,GAAG,YAAY,CAAC;EACvH,SAASE,QAAQA,CAACC,IAAI,EAAEH,IAAI,EAAE;IAC5BG,IAAI,CAACR,EAAE,GAAG;MAAA,OAAaG,gBAAgB,CAACD,UAAU,CAACO,WAAW,CAACT,EAAE,CAAC,GAAAU,SAAO,CAAC,EAAEL,IAAI,CAAC;IAAA;IACjFG,IAAI,CAACG,IAAI,GAAG;MAAA,OAAaR,gBAAgB,CAACD,UAAU,CAACO,WAAW,CAACE,IAAI,CAAC,GAAAD,SAAO,CAAC,EAAEL,IAAI,CAAC;IAAA;IACrFG,IAAI,CAACI,OAAO,GAAG;MAAA,OAAaT,gBAAgB,CAACD,UAAU,CAACO,WAAW,CAACG,OAAO,CAAC,GAAAF,SAAO,CAAC,EAAEL,IAAI,CAAC;IAAA;IAC3FG,IAAI,CAACK,IAAI,GAAG;MAAA,OAAaV,gBAAgB,CAACD,UAAU,CAACO,WAAW,CAACI,IAAI,CAAC,GAAAH,SAAO,CAAC,EAAEL,IAAI,CAAC;IAAA;IACrFG,IAAI,CAACM,GAAG,GAAG,YAAa;MACtB,MAAMC,MAAM,GAAGZ,gBAAgB,CAACD,UAAU,CAACO,WAAW,CAACK,GAAG,CAAC,GAAAJ,SAAO,CAAC,EAAEL,IAAI,CAAC;MAC1E,IAAIU,MAAM,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;QAClC;QACA,OAAOD,MAAM,CAACT,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;MAChI;MACA,OAAOS,MAAM;IACf,CAAC;EACH;EACA,MAAMP,IAAI,GAAG,CAAC,CAAC;EACf,MAAMtC,gBAAgB,GAAGmC,IAAI,IAAI;IAC/BE,QAAQ,CAACC,IAAI,EAAEH,IAAI,CAAC;IACpB,OAAOG,IAAI;EACb,CAAC;EACDD,QAAQ,CAACrC,gBAAgB,CAAC;EAC1B,OAAO;IACL,GAAGgC,UAAU;IACbhC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}