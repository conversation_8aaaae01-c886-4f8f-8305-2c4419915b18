{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\n/**\n * Lazy initialization container for the Ripple instance. This improves\n * performance by delaying mounting the ripple until it's needed.\n */\nexport class LazyRipple {\n  /** React ref to the ripple instance */\n\n  /** If the ripple component should be mounted */\n\n  /** Promise that resolves when the ripple component is mounted */\n\n  /** If the ripple component has been mounted */\n\n  /** React state hook setter */\n\n  static create() {\n    return new LazyRipple();\n  }\n  static use() {\n    /* eslint-disable */\n    const ripple = useLazyRef(LazyRipple.create).current;\n    const [shouldMount, setShouldMount] = React.useState(false);\n    ripple.shouldMount = shouldMount;\n    ripple.setShouldMount = setShouldMount;\n    React.useEffect(ripple.mountEffect, [shouldMount]);\n    /* eslint-enable */\n\n    return ripple;\n  }\n  constructor() {\n    this.ref = {\n      current: null\n    };\n    this.mounted = null;\n    this.didMount = false;\n    this.shouldMount = false;\n    this.setShouldMount = null;\n  }\n  mount() {\n    if (!this.mounted) {\n      this.mounted = createControlledPromise();\n      this.shouldMount = true;\n      this.setShouldMount(this.shouldMount);\n    }\n    return this.mounted;\n  }\n  mountEffect = () => {\n    if (this.shouldMount && !this.didMount) {\n      if (this.ref.current !== null) {\n        this.didMount = true;\n        this.mounted.resolve();\n      }\n    }\n  };\n\n  /* Ripple API */\n\n  start() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    this.mount().then(() => this.ref.current?.start(...args));\n  }\n  stop() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    this.mount().then(() => this.ref.current?.stop(...args));\n  }\n  pulsate() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    this.mount().then(() => this.ref.current?.pulsate(...args));\n  }\n}\nexport default function useLazyRipple() {\n  return LazyRipple.use();\n}\nfunction createControlledPromise() {\n  let resolve;\n  let reject;\n  const p = new Promise((resolveFn, rejectFn) => {\n    resolve = resolveFn;\n    reject = rejectFn;\n  });\n  p.resolve = resolve;\n  p.reject = reject;\n  return p;\n}", "map": {"version": 3, "names": ["React", "useLazyRef", "<PERSON>zyR<PERSON>ple", "create", "use", "ripple", "current", "shouldMount", "setShouldMount", "useState", "useEffect", "mountEffect", "constructor", "ref", "mounted", "didMount", "mount", "createControlledPromise", "resolve", "start", "_len", "arguments", "length", "args", "Array", "_key", "then", "stop", "_len2", "_key2", "pulsate", "_len3", "_key3", "useLazyRipple", "reject", "p", "Promise", "resolveFn", "rejectFn"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/useLazyRipple/useLazyRipple.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\n/**\n * Lazy initialization container for the Ripple instance. This improves\n * performance by delaying mounting the ripple until it's needed.\n */\nexport class LazyRipple {\n  /** React ref to the ripple instance */\n\n  /** If the ripple component should be mounted */\n\n  /** Promise that resolves when the ripple component is mounted */\n\n  /** If the ripple component has been mounted */\n\n  /** React state hook setter */\n\n  static create() {\n    return new LazyRipple();\n  }\n  static use() {\n    /* eslint-disable */\n    const ripple = useLazyRef(LazyRipple.create).current;\n    const [shouldMount, setShouldMount] = React.useState(false);\n    ripple.shouldMount = shouldMount;\n    ripple.setShouldMount = setShouldMount;\n    React.useEffect(ripple.mountEffect, [shouldMount]);\n    /* eslint-enable */\n\n    return ripple;\n  }\n  constructor() {\n    this.ref = {\n      current: null\n    };\n    this.mounted = null;\n    this.didMount = false;\n    this.shouldMount = false;\n    this.setShouldMount = null;\n  }\n  mount() {\n    if (!this.mounted) {\n      this.mounted = createControlledPromise();\n      this.shouldMount = true;\n      this.setShouldMount(this.shouldMount);\n    }\n    return this.mounted;\n  }\n  mountEffect = () => {\n    if (this.shouldMount && !this.didMount) {\n      if (this.ref.current !== null) {\n        this.didMount = true;\n        this.mounted.resolve();\n      }\n    }\n  };\n\n  /* Ripple API */\n\n  start(...args) {\n    this.mount().then(() => this.ref.current?.start(...args));\n  }\n  stop(...args) {\n    this.mount().then(() => this.ref.current?.stop(...args));\n  }\n  pulsate(...args) {\n    this.mount().then(() => this.ref.current?.pulsate(...args));\n  }\n}\nexport default function useLazyRipple() {\n  return LazyRipple.use();\n}\nfunction createControlledPromise() {\n  let resolve;\n  let reject;\n  const p = new Promise((resolveFn, rejectFn) => {\n    resolve = resolveFn;\n    reject = rejectFn;\n  });\n  p.resolve = resolve;\n  p.reject = reject;\n  return p;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,CAAC;EACtB;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA,OAAOC,MAAMA,CAAA,EAAG;IACd,OAAO,IAAID,UAAU,CAAC,CAAC;EACzB;EACA,OAAOE,GAAGA,CAAA,EAAG;IACX;IACA,MAAMC,MAAM,GAAGJ,UAAU,CAACC,UAAU,CAACC,MAAM,CAAC,CAACG,OAAO;IACpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC,KAAK,CAAC;IAC3DJ,MAAM,CAACE,WAAW,GAAGA,WAAW;IAChCF,MAAM,CAACG,cAAc,GAAGA,cAAc;IACtCR,KAAK,CAACU,SAAS,CAACL,MAAM,CAACM,WAAW,EAAE,CAACJ,WAAW,CAAC,CAAC;IAClD;;IAEA,OAAOF,MAAM;EACf;EACAO,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,GAAG,GAAG;MACTP,OAAO,EAAE;IACX,CAAC;IACD,IAAI,CAACQ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACR,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EACAQ,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;MACjB,IAAI,CAACA,OAAO,GAAGG,uBAAuB,CAAC,CAAC;MACxC,IAAI,CAACV,WAAW,GAAG,IAAI;MACvB,IAAI,CAACC,cAAc,CAAC,IAAI,CAACD,WAAW,CAAC;IACvC;IACA,OAAO,IAAI,CAACO,OAAO;EACrB;EACAH,WAAW,GAAGA,CAAA,KAAM;IAClB,IAAI,IAAI,CAACJ,WAAW,IAAI,CAAC,IAAI,CAACQ,QAAQ,EAAE;MACtC,IAAI,IAAI,CAACF,GAAG,CAACP,OAAO,KAAK,IAAI,EAAE;QAC7B,IAAI,CAACS,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACD,OAAO,CAACI,OAAO,CAAC,CAAC;MACxB;IACF;EACF,CAAC;;EAED;;EAEAC,KAAKA,CAAA,EAAU;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACX,IAAI,CAACT,KAAK,CAAC,CAAC,CAACU,IAAI,CAAC,MAAM,IAAI,CAACb,GAAG,CAACP,OAAO,EAAEa,KAAK,CAAC,GAAGI,IAAI,CAAC,CAAC;EAC3D;EACAI,IAAIA,CAAA,EAAU;IAAA,SAAAC,KAAA,GAAAP,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAI,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJN,IAAI,CAAAM,KAAA,IAAAR,SAAA,CAAAQ,KAAA;IAAA;IACV,IAAI,CAACb,KAAK,CAAC,CAAC,CAACU,IAAI,CAAC,MAAM,IAAI,CAACb,GAAG,CAACP,OAAO,EAAEqB,IAAI,CAAC,GAAGJ,IAAI,CAAC,CAAC;EAC1D;EACAO,OAAOA,CAAA,EAAU;IAAA,SAAAC,KAAA,GAAAV,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAO,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJT,IAAI,CAAAS,KAAA,IAAAX,SAAA,CAAAW,KAAA;IAAA;IACb,IAAI,CAAChB,KAAK,CAAC,CAAC,CAACU,IAAI,CAAC,MAAM,IAAI,CAACb,GAAG,CAACP,OAAO,EAAEwB,OAAO,CAAC,GAAGP,IAAI,CAAC,CAAC;EAC7D;AACF;AACA,eAAe,SAASU,aAAaA,CAAA,EAAG;EACtC,OAAO/B,UAAU,CAACE,GAAG,CAAC,CAAC;AACzB;AACA,SAASa,uBAAuBA,CAAA,EAAG;EACjC,IAAIC,OAAO;EACX,IAAIgB,MAAM;EACV,MAAMC,CAAC,GAAG,IAAIC,OAAO,CAAC,CAACC,SAAS,EAAEC,QAAQ,KAAK;IAC7CpB,OAAO,GAAGmB,SAAS;IACnBH,MAAM,GAAGI,QAAQ;EACnB,CAAC,CAAC;EACFH,CAAC,CAACjB,OAAO,GAAGA,OAAO;EACnBiB,CAAC,CAACD,MAAM,GAAGA,MAAM;EACjB,OAAOC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}