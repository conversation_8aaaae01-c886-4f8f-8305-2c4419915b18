{"ast": null, "code": "export { default } from \"./ponyfillGlobal.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/utils/esm/ponyfillGlobal/index.js"], "sourcesContent": ["export { default } from \"./ponyfillGlobal.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}