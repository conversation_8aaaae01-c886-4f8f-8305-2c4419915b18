{"ast": null, "code": "// Determine if the toggle button value matches, or is contained in, the\n// candidate group value.\nexport default function isValueSelected(value, candidate) {\n  if (candidate === undefined || value === undefined) {\n    return false;\n  }\n  if (Array.isArray(candidate)) {\n    return candidate.includes(value);\n  }\n  return value === candidate;\n}", "map": {"version": 3, "names": ["isValueSelected", "value", "candidate", "undefined", "Array", "isArray", "includes"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/ToggleButtonGroup/isValueSelected.js"], "sourcesContent": ["// Determine if the toggle button value matches, or is contained in, the\n// candidate group value.\nexport default function isValueSelected(value, candidate) {\n  if (candidate === undefined || value === undefined) {\n    return false;\n  }\n  if (Array.isArray(candidate)) {\n    return candidate.includes(value);\n  }\n  return value === candidate;\n}"], "mappings": "AAAA;AACA;AACA,eAAe,SAASA,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACxD,IAAIA,SAAS,KAAKC,SAAS,IAAIF,KAAK,KAAKE,SAAS,EAAE;IAClD,OAAO,KAAK;EACd;EACA,IAAIC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,EAAE;IAC5B,OAAOA,SAAS,CAACI,QAAQ,CAACL,KAAK,CAAC;EAClC;EACA,OAAOA,KAAK,KAAKC,SAAS;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}