{"ast": null, "code": "import prepareCssVars from \"./prepareCssVars.js\";\nimport { createGetColorSchemeSelector } from \"./getColorSchemeSelector.js\";\nimport { DEFAULT_ATTRIBUTE } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nfunction createCssVarsTheme(_ref) {\n  let {\n    colorSchemeSelector = `[${DEFAULT_ATTRIBUTE}=\"%s\"]`,\n    ...theme\n  } = _ref;\n  const output = theme;\n  const result = prepareCssVars(output, {\n    ...theme,\n    prefix: theme.cssVarPrefix,\n    colorSchemeSelector\n  });\n  output.vars = result.vars;\n  output.generateThemeVars = result.generateThemeVars;\n  output.generateStyleSheets = result.generateStyleSheets;\n  output.colorSchemeSelector = colorSchemeSelector;\n  output.getColorSchemeSelector = createGetColorSchemeSelector(colorSchemeSelector);\n  return output;\n}\nexport default createCssVarsTheme;", "map": {"version": 3, "names": ["prepareCssVars", "createGetColorSchemeSelector", "DEFAULT_ATTRIBUTE", "createCssVarsTheme", "_ref", "colorSchemeSelector", "theme", "output", "result", "prefix", "cssVarPrefix", "vars", "generateThemeVars", "generateStyleSheets", "getColorSchemeSelector"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/system/esm/cssVars/createCssVarsTheme.js"], "sourcesContent": ["import prepareCssVars from \"./prepareCssVars.js\";\nimport { createGetColorSchemeSelector } from \"./getColorSchemeSelector.js\";\nimport { DEFAULT_ATTRIBUTE } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nfunction createCssVarsTheme({\n  colorSchemeSelector = `[${DEFAULT_ATTRIBUTE}=\"%s\"]`,\n  ...theme\n}) {\n  const output = theme;\n  const result = prepareCssVars(output, {\n    ...theme,\n    prefix: theme.cssVarPrefix,\n    colorSchemeSelector\n  });\n  output.vars = result.vars;\n  output.generateThemeVars = result.generateThemeVars;\n  output.generateStyleSheets = result.generateStyleSheets;\n  output.colorSchemeSelector = colorSchemeSelector;\n  output.getColorSchemeSelector = createGetColorSchemeSelector(colorSchemeSelector);\n  return output;\n}\nexport default createCssVarsTheme;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,SAASC,4BAA4B,QAAQ,6BAA6B;AAC1E,SAASC,iBAAiB,QAAQ,mDAAmD;AACrF,SAASC,kBAAkBA,CAAAC,IAAA,EAGxB;EAAA,IAHyB;IAC1BC,mBAAmB,GAAG,IAAIH,iBAAiB,QAAQ;IACnD,GAAGI;EACL,CAAC,GAAAF,IAAA;EACC,MAAMG,MAAM,GAAGD,KAAK;EACpB,MAAME,MAAM,GAAGR,cAAc,CAACO,MAAM,EAAE;IACpC,GAAGD,KAAK;IACRG,MAAM,EAAEH,KAAK,CAACI,YAAY;IAC1BL;EACF,CAAC,CAAC;EACFE,MAAM,CAACI,IAAI,GAAGH,MAAM,CAACG,IAAI;EACzBJ,MAAM,CAACK,iBAAiB,GAAGJ,MAAM,CAACI,iBAAiB;EACnDL,MAAM,CAACM,mBAAmB,GAAGL,MAAM,CAACK,mBAAmB;EACvDN,MAAM,CAACF,mBAAmB,GAAGA,mBAAmB;EAChDE,MAAM,CAACO,sBAAsB,GAAGb,4BAA4B,CAACI,mBAAmB,CAAC;EACjF,OAAOE,MAAM;AACf;AACA,eAAeJ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}