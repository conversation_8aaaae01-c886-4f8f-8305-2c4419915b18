{"ast": null, "code": "import excludeVariablesFromRoot from \"./excludeVariablesFromRoot.js\";\nexport default theme => (colorScheme, css) => {\n  const root = theme.rootSelector || ':root';\n  const selector = theme.colorSchemeSelector;\n  let rule = selector;\n  if (selector === 'class') {\n    rule = '.%s';\n  }\n  if (selector === 'data') {\n    rule = '[data-%s]';\n  }\n  if (selector?.startsWith('data-') && !selector.includes('%s')) {\n    // 'data-mui-color-scheme' -> '[data-mui-color-scheme=\"%s\"]'\n    rule = `[${selector}=\"%s\"]`;\n  }\n  if (theme.defaultColorScheme === colorScheme) {\n    if (colorScheme === 'dark') {\n      const excludedVariables = {};\n      excludeVariablesFromRoot(theme.cssVarPrefix).forEach(cssVar => {\n        excludedVariables[cssVar] = css[cssVar];\n        delete css[cssVar];\n      });\n      if (rule === 'media') {\n        return {\n          [root]: css,\n          [`@media (prefers-color-scheme: dark)`]: {\n            [root]: excludedVariables\n          }\n        };\n      }\n      if (rule) {\n        return {\n          [rule.replace('%s', colorScheme)]: excludedVariables,\n          [`${root}, ${rule.replace('%s', colorScheme)}`]: css\n        };\n      }\n      return {\n        [root]: {\n          ...css,\n          ...excludedVariables\n        }\n      };\n    }\n    if (rule && rule !== 'media') {\n      return `${root}, ${rule.replace('%s', String(colorScheme))}`;\n    }\n  } else if (colorScheme) {\n    if (rule === 'media') {\n      return {\n        [`@media (prefers-color-scheme: ${String(colorScheme)})`]: {\n          [root]: css\n        }\n      };\n    }\n    if (rule) {\n      return rule.replace('%s', String(colorScheme));\n    }\n  }\n  return root;\n};", "map": {"version": 3, "names": ["excludeVariablesFromRoot", "theme", "colorScheme", "css", "root", "rootSelector", "selector", "colorSchemeSelector", "rule", "startsWith", "includes", "defaultColorScheme", "excludedVariables", "cssVarPrefix", "for<PERSON>ach", "cssVar", "replace", "String"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/material/styles/createGetSelector.js"], "sourcesContent": ["import excludeVariablesFromRoot from \"./excludeVariablesFromRoot.js\";\nexport default theme => (colorScheme, css) => {\n  const root = theme.rootSelector || ':root';\n  const selector = theme.colorSchemeSelector;\n  let rule = selector;\n  if (selector === 'class') {\n    rule = '.%s';\n  }\n  if (selector === 'data') {\n    rule = '[data-%s]';\n  }\n  if (selector?.startsWith('data-') && !selector.includes('%s')) {\n    // 'data-mui-color-scheme' -> '[data-mui-color-scheme=\"%s\"]'\n    rule = `[${selector}=\"%s\"]`;\n  }\n  if (theme.defaultColorScheme === colorScheme) {\n    if (colorScheme === 'dark') {\n      const excludedVariables = {};\n      excludeVariablesFromRoot(theme.cssVarPrefix).forEach(cssVar => {\n        excludedVariables[cssVar] = css[cssVar];\n        delete css[cssVar];\n      });\n      if (rule === 'media') {\n        return {\n          [root]: css,\n          [`@media (prefers-color-scheme: dark)`]: {\n            [root]: excludedVariables\n          }\n        };\n      }\n      if (rule) {\n        return {\n          [rule.replace('%s', colorScheme)]: excludedVariables,\n          [`${root}, ${rule.replace('%s', colorScheme)}`]: css\n        };\n      }\n      return {\n        [root]: {\n          ...css,\n          ...excludedVariables\n        }\n      };\n    }\n    if (rule && rule !== 'media') {\n      return `${root}, ${rule.replace('%s', String(colorScheme))}`;\n    }\n  } else if (colorScheme) {\n    if (rule === 'media') {\n      return {\n        [`@media (prefers-color-scheme: ${String(colorScheme)})`]: {\n          [root]: css\n        }\n      };\n    }\n    if (rule) {\n      return rule.replace('%s', String(colorScheme));\n    }\n  }\n  return root;\n};"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,+BAA+B;AACpE,eAAeC,KAAK,IAAI,CAACC,WAAW,EAAEC,GAAG,KAAK;EAC5C,MAAMC,IAAI,GAAGH,KAAK,CAACI,YAAY,IAAI,OAAO;EAC1C,MAAMC,QAAQ,GAAGL,KAAK,CAACM,mBAAmB;EAC1C,IAAIC,IAAI,GAAGF,QAAQ;EACnB,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACxBE,IAAI,GAAG,KAAK;EACd;EACA,IAAIF,QAAQ,KAAK,MAAM,EAAE;IACvBE,IAAI,GAAG,WAAW;EACpB;EACA,IAAIF,QAAQ,EAAEG,UAAU,CAAC,OAAO,CAAC,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC7D;IACAF,IAAI,GAAG,IAAIF,QAAQ,QAAQ;EAC7B;EACA,IAAIL,KAAK,CAACU,kBAAkB,KAAKT,WAAW,EAAE;IAC5C,IAAIA,WAAW,KAAK,MAAM,EAAE;MAC1B,MAAMU,iBAAiB,GAAG,CAAC,CAAC;MAC5BZ,wBAAwB,CAACC,KAAK,CAACY,YAAY,CAAC,CAACC,OAAO,CAACC,MAAM,IAAI;QAC7DH,iBAAiB,CAACG,MAAM,CAAC,GAAGZ,GAAG,CAACY,MAAM,CAAC;QACvC,OAAOZ,GAAG,CAACY,MAAM,CAAC;MACpB,CAAC,CAAC;MACF,IAAIP,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO;UACL,CAACJ,IAAI,GAAGD,GAAG;UACX,CAAC,qCAAqC,GAAG;YACvC,CAACC,IAAI,GAAGQ;UACV;QACF,CAAC;MACH;MACA,IAAIJ,IAAI,EAAE;QACR,OAAO;UACL,CAACA,IAAI,CAACQ,OAAO,CAAC,IAAI,EAAEd,WAAW,CAAC,GAAGU,iBAAiB;UACpD,CAAC,GAAGR,IAAI,KAAKI,IAAI,CAACQ,OAAO,CAAC,IAAI,EAAEd,WAAW,CAAC,EAAE,GAAGC;QACnD,CAAC;MACH;MACA,OAAO;QACL,CAACC,IAAI,GAAG;UACN,GAAGD,GAAG;UACN,GAAGS;QACL;MACF,CAAC;IACH;IACA,IAAIJ,IAAI,IAAIA,IAAI,KAAK,OAAO,EAAE;MAC5B,OAAO,GAAGJ,IAAI,KAAKI,IAAI,CAACQ,OAAO,CAAC,IAAI,EAAEC,MAAM,CAACf,WAAW,CAAC,CAAC,EAAE;IAC9D;EACF,CAAC,MAAM,IAAIA,WAAW,EAAE;IACtB,IAAIM,IAAI,KAAK,OAAO,EAAE;MACpB,OAAO;QACL,CAAC,iCAAiCS,MAAM,CAACf,WAAW,CAAC,GAAG,GAAG;UACzD,CAACE,IAAI,GAAGD;QACV;MACF,CAAC;IACH;IACA,IAAIK,IAAI,EAAE;MACR,OAAOA,IAAI,CAACQ,OAAO,CAAC,IAAI,EAAEC,MAAM,CAACf,WAAW,CAAC,CAAC;IAChD;EACF;EACA,OAAOE,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}