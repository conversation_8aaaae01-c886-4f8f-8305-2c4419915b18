{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport styled from '@mui/styled-engine';\nimport styleFunctionSx, { extendSxProp } from \"../styleFunctionSx/index.js\";\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createBox() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    themeId,\n    defaultTheme,\n    defaultClassName = 'MuiBox-root',\n    generateClassName\n  } = options;\n  const BoxRoot = styled('div', {\n    shouldForwardProp: prop => prop !== 'theme' && prop !== 'sx' && prop !== 'as'\n  })(styleFunctionSx);\n  const Box = /*#__PURE__*/React.forwardRef(function Box(inProps, ref) {\n    const theme = useTheme(defaultTheme);\n    const {\n      className,\n      component = 'div',\n      ...other\n    } = extendSxProp(inProps);\n    return /*#__PURE__*/_jsx(BoxRoot, {\n      as: component,\n      ref: ref,\n      className: clsx(className, generateClassName ? generateClassName(defaultClassName) : defaultClassName),\n      theme: themeId ? theme[themeId] || theme : theme,\n      ...other\n    });\n  });\n  return Box;\n}", "map": {"version": 3, "names": ["React", "clsx", "styled", "styleFunctionSx", "extendSxProp", "useTheme", "jsx", "_jsx", "createBox", "options", "arguments", "length", "undefined", "themeId", "defaultTheme", "defaultClassName", "generateClassName", "BoxRoot", "shouldForwardProp", "prop", "Box", "forwardRef", "inProps", "ref", "theme", "className", "component", "other", "as"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/system/esm/createBox/createBox.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport styled from '@mui/styled-engine';\nimport styleFunctionSx, { extendSxProp } from \"../styleFunctionSx/index.js\";\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createBox(options = {}) {\n  const {\n    themeId,\n    defaultTheme,\n    defaultClassName = 'MuiBox-root',\n    generateClassName\n  } = options;\n  const BoxRoot = styled('div', {\n    shouldForwardProp: prop => prop !== 'theme' && prop !== 'sx' && prop !== 'as'\n  })(styleFunctionSx);\n  const Box = /*#__PURE__*/React.forwardRef(function Box(inProps, ref) {\n    const theme = useTheme(defaultTheme);\n    const {\n      className,\n      component = 'div',\n      ...other\n    } = extendSxProp(inProps);\n    return /*#__PURE__*/_jsx(BoxRoot, {\n      as: component,\n      ref: ref,\n      className: clsx(className, generateClassName ? generateClassName(defaultClassName) : defaultClassName),\n      theme: themeId ? theme[themeId] || theme : theme,\n      ...other\n    });\n  });\n  return Box;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,eAAe,IAAIC,YAAY,QAAQ,6BAA6B;AAC3E,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,SAASA,CAAA,EAAe;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC5C,MAAM;IACJG,OAAO;IACPC,YAAY;IACZC,gBAAgB,GAAG,aAAa;IAChCC;EACF,CAAC,GAAGP,OAAO;EACX,MAAMQ,OAAO,GAAGf,MAAM,CAAC,KAAK,EAAE;IAC5BgB,iBAAiB,EAAEC,IAAI,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK;EAC3E,CAAC,CAAC,CAAChB,eAAe,CAAC;EACnB,MAAMiB,GAAG,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,SAASD,GAAGA,CAACE,OAAO,EAAEC,GAAG,EAAE;IACnE,MAAMC,KAAK,GAAGnB,QAAQ,CAACS,YAAY,CAAC;IACpC,MAAM;MACJW,SAAS;MACTC,SAAS,GAAG,KAAK;MACjB,GAAGC;IACL,CAAC,GAAGvB,YAAY,CAACkB,OAAO,CAAC;IACzB,OAAO,aAAaf,IAAI,CAACU,OAAO,EAAE;MAChCW,EAAE,EAAEF,SAAS;MACbH,GAAG,EAAEA,GAAG;MACRE,SAAS,EAAExB,IAAI,CAACwB,SAAS,EAAET,iBAAiB,GAAGA,iBAAiB,CAACD,gBAAgB,CAAC,GAAGA,gBAAgB,CAAC;MACtGS,KAAK,EAAEX,OAAO,GAAGW,KAAK,CAACX,OAAO,CAAC,IAAIW,KAAK,GAAGA,KAAK;MAChD,GAAGG;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOP,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}