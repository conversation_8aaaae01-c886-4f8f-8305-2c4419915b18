{"ast": null, "code": "export { default } from \"./useSlotProps.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/utils/esm/useSlotProps/index.js"], "sourcesContent": ["export { default } from \"./useSlotProps.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}