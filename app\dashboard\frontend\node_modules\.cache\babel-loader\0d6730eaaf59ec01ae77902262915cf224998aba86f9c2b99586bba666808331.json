{"ast": null, "code": "const defaultGenerator = componentName => componentName;\nconst createClassNameGenerator = () => {\n  let generate = defaultGenerator;\n  return {\n    configure(generator) {\n      generate = generator;\n    },\n    generate(componentName) {\n      return generate(componentName);\n    },\n    reset() {\n      generate = defaultGenerator;\n    }\n  };\n};\nconst ClassNameGenerator = createClassNameGenerator();\nexport default ClassNameGenerator;", "map": {"version": 3, "names": ["defaultGenerator", "componentName", "createClassNameGenerator", "generate", "configure", "generator", "reset", "ClassNameGenerator"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/utils/esm/ClassNameGenerator/ClassNameGenerator.js"], "sourcesContent": ["const defaultGenerator = componentName => componentName;\nconst createClassNameGenerator = () => {\n  let generate = defaultGenerator;\n  return {\n    configure(generator) {\n      generate = generator;\n    },\n    generate(componentName) {\n      return generate(componentName);\n    },\n    reset() {\n      generate = defaultGenerator;\n    }\n  };\n};\nconst ClassNameGenerator = createClassNameGenerator();\nexport default ClassNameGenerator;"], "mappings": "AAAA,MAAMA,gBAAgB,GAAGC,aAAa,IAAIA,aAAa;AACvD,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;EACrC,IAAIC,QAAQ,GAAGH,gBAAgB;EAC/B,OAAO;IACLI,SAASA,CAACC,SAAS,EAAE;MACnBF,QAAQ,GAAGE,SAAS;IACtB,CAAC;IACDF,QAAQA,CAACF,aAAa,EAAE;MACtB,OAAOE,QAAQ,CAACF,aAAa,CAAC;IAChC,CAAC;IACDK,KAAKA,CAAA,EAAG;MACNH,QAAQ,GAAGH,gBAAgB;IAC7B;EACF,CAAC;AACH,CAAC;AACD,MAAMO,kBAAkB,GAAGL,wBAAwB,CAAC,CAAC;AACrD,eAAeK,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}