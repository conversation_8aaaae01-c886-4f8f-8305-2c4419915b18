{"ast": null, "code": "import ownerDocument from \"../ownerDocument/index.js\";\nexport default function ownerWindow(node) {\n  const doc = ownerDocument(node);\n  return doc.defaultView || window;\n}", "map": {"version": 3, "names": ["ownerDocument", "ownerWindow", "node", "doc", "defaultView", "window"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/utils/esm/ownerWindow/ownerWindow.js"], "sourcesContent": ["import ownerDocument from \"../ownerDocument/index.js\";\nexport default function ownerWindow(node) {\n  const doc = ownerDocument(node);\n  return doc.defaultView || window;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,2BAA2B;AACrD,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,MAAMC,GAAG,GAAGH,aAAa,CAACE,IAAI,CAAC;EAC/B,OAAOC,GAAG,CAACC,WAAW,IAAIC,MAAM;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}