[{"C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\contexts\\ThemeContext.tsx": "3", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\AutoTradeControl.tsx": "4", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\Login.tsx": "6", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\ProtectedRoute.tsx": "7", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\api.ts": "8", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\MLOptimization.tsx": "9", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\SessionReports.tsx": "10", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\BinanceAccount.tsx": "11", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\StrategyEnsemble.tsx": "12", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\MLDashboard.tsx": "13", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\ErrorBoundary.tsx": "14", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\DashboardLayout.tsx": "15", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\TradeDashboard.tsx": "16", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\MLControls.tsx": "17", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\BinanceAccountPanel.tsx": "18", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\authService.ts": "19", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\websocket.ts": "20", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\tokenService.ts": "21", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\LoadingFallback.tsx": "22", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\session\\SessionDetails.tsx": "23", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\config.ts": "24", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\apiService.ts": "25", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\analyticsWebsocket.ts": "26", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\utils\\formatters.ts": "27", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\types\\binance.ts": "28", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\common\\TradesTable.tsx": "29", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\account\\AccountStatistics.tsx": "30", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\MLControls.js": "31"}, {"size": 486, "mtime": *************, "results": "32", "hashOfConfig": "33"}, {"size": 1396, "mtime": *************, "results": "34", "hashOfConfig": "33"}, {"size": 3911, "mtime": *************, "results": "35", "hashOfConfig": "33"}, {"size": 5941, "mtime": *************, "results": "36", "hashOfConfig": "33"}, {"size": 5530, "mtime": *************, "results": "37", "hashOfConfig": "33"}, {"size": 3709, "mtime": *************, "results": "38", "hashOfConfig": "33"}, {"size": 792, "mtime": *************, "results": "39", "hashOfConfig": "33"}, {"size": 6434, "mtime": *************, "results": "40", "hashOfConfig": "33"}, {"size": 739, "mtime": *************, "results": "41", "hashOfConfig": "33"}, {"size": 45744, "mtime": *************, "results": "42", "hashOfConfig": "33"}, {"size": 15601, "mtime": *************, "results": "43", "hashOfConfig": "33"}, {"size": 32943, "mtime": 1750430998696, "results": "44", "hashOfConfig": "33"}, {"size": 33411, "mtime": 1750430998690, "results": "45", "hashOfConfig": "33"}, {"size": 2140, "mtime": 1750342947868, "results": "46", "hashOfConfig": "33"}, {"size": 4103, "mtime": 1750430998683, "results": "47", "hashOfConfig": "33"}, {"size": 12810, "mtime": 1750493316669, "results": "48", "hashOfConfig": "33"}, {"size": 11579, "mtime": 1750342947949, "results": "49", "hashOfConfig": "33"}, {"size": 35518, "mtime": 1750342947797, "results": "50", "hashOfConfig": "33"}, {"size": 2150, "mtime": 1750342948450, "results": "51", "hashOfConfig": "33"}, {"size": 8301, "mtime": 1750494588370, "results": "52", "hashOfConfig": "33"}, {"size": 3770, "mtime": 1750342948469, "results": "53", "hashOfConfig": "33"}, {"size": 693, "mtime": 1750342947897, "results": "54", "hashOfConfig": "33"}, {"size": 25561, "mtime": 1750342948111, "results": "55", "hashOfConfig": "33"}, {"size": 420, "mtime": 1750342948124, "results": "56", "hashOfConfig": "33"}, {"size": 2682, "mtime": 1750342948426, "results": "57", "hashOfConfig": "33"}, {"size": 4986, "mtime": 1750342948381, "results": "58", "hashOfConfig": "33"}, {"size": 772, "mtime": 1750342948639, "results": "59", "hashOfConfig": "33"}, {"size": 7922, "mtime": 1750342948533, "results": "60", "hashOfConfig": "33"}, {"size": 3643, "mtime": 1750342948072, "results": "61", "hashOfConfig": "33"}, {"size": 1891, "mtime": 1750342948034, "results": "62", "hashOfConfig": "33"}, {"size": 10609, "mtime": 1750342947917, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8e78f1", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\AutoTradeControl.tsx", [], ["157"], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\api.ts", ["158"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\MLOptimization.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\SessionReports.tsx", ["159", "160", "161", "162", "163", "164", "165", "166"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\BinanceAccount.tsx", ["167"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\StrategyEnsemble.tsx", ["168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\MLDashboard.tsx", ["184", "185", "186", "187", "188", "189", "190"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\TradeDashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\MLControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\BinanceAccountPanel.tsx", ["191", "192", "193", "194", "195", "196", "197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\authService.ts", ["210"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\websocket.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\tokenService.ts", ["211"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\LoadingFallback.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\session\\SessionDetails.tsx", ["212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\apiService.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\analyticsWebsocket.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\utils\\formatters.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\types\\binance.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\common\\TradesTable.tsx", ["233"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\account\\AccountStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\MLControls.js", [], [], {"ruleId": "234", "severity": 1, "message": "235", "line": 116, "column": 6, "nodeType": "236", "endLine": 116, "endColumn": 8, "suggestions": "237", "suppressions": "238"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 5, "column": 7, "nodeType": "241", "messageId": "242", "endLine": 5, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "243", "line": 28, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 28, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "244", "line": 36, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 36, "endColumn": 31}, {"ruleId": "239", "severity": 1, "message": "245", "line": 44, "column": 19, "nodeType": "241", "messageId": "242", "endLine": 44, "endColumn": 35}, {"ruleId": "239", "severity": 1, "message": "246", "line": 46, "column": 13, "nodeType": "241", "messageId": "242", "endLine": 46, "endColumn": 23}, {"ruleId": "239", "severity": 1, "message": "247", "line": 51, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 51, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "248", "line": 52, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 52, "endColumn": 7}, {"ruleId": "234", "severity": 1, "message": "249", "line": 244, "column": 6, "nodeType": "236", "endLine": 244, "endColumn": 32, "suggestions": "250"}, {"ruleId": "234", "severity": 1, "message": "251", "line": 598, "column": 8, "nodeType": "236", "endLine": 598, "endColumn": 26, "suggestions": "252"}, {"ruleId": "239", "severity": 1, "message": "253", "line": 33, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 33, "endColumn": 8}, {"ruleId": "239", "severity": 1, "message": "253", "line": 5, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 5, "endColumn": 8}, {"ruleId": "239", "severity": 1, "message": "254", "line": 19, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 19, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "255", "line": 28, "column": 15, "nodeType": "241", "messageId": "242", "endLine": 28, "endColumn": 27}, {"ruleId": "239", "severity": 1, "message": "256", "line": 42, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 42, "endColumn": 11}, {"ruleId": "239", "severity": 1, "message": "257", "line": 43, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 43, "endColumn": 6}, {"ruleId": "239", "severity": 1, "message": "248", "line": 44, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 44, "endColumn": 7}, {"ruleId": "239", "severity": 1, "message": "247", "line": 45, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 45, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "258", "line": 63, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 63, "endColumn": 17}, {"ruleId": "239", "severity": 1, "message": "259", "line": 64, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 64, "endColumn": 17}, {"ruleId": "239", "severity": 1, "message": "260", "line": 65, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 65, "endColumn": 27}, {"ruleId": "239", "severity": 1, "message": "261", "line": 66, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 66, "endColumn": 22}, {"ruleId": "239", "severity": 1, "message": "262", "line": 67, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 67, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "263", "line": 68, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 68, "endColumn": 16}, {"ruleId": "239", "severity": 1, "message": "264", "line": 69, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 69, "endColumn": 23}, {"ruleId": "239", "severity": 1, "message": "265", "line": 82, "column": 7, "nodeType": "241", "messageId": "242", "endLine": 82, "endColumn": 25}, {"ruleId": "239", "severity": 1, "message": "266", "line": 315, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 315, "endColumn": 23}, {"ruleId": "239", "severity": 1, "message": "267", "line": 23, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 23, "endColumn": 31}, {"ruleId": "239", "severity": 1, "message": "268", "line": 31, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 31, "endColumn": 25}, {"ruleId": "239", "severity": 1, "message": "269", "line": 46, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 46, "endColumn": 11}, {"ruleId": "239", "severity": 1, "message": "270", "line": 47, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 47, "endColumn": 6}, {"ruleId": "239", "severity": 1, "message": "271", "line": 48, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 48, "endColumn": 7}, {"ruleId": "239", "severity": 1, "message": "272", "line": 49, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 49, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "273", "line": 50, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 50, "endColumn": 10}, {"ruleId": "239", "severity": 1, "message": "274", "line": 43, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 43, "endColumn": 9}, {"ruleId": "239", "severity": 1, "message": "275", "line": 44, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 44, "endColumn": 11}, {"ruleId": "239", "severity": 1, "message": "276", "line": 45, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 45, "endColumn": 14}, {"ruleId": "239", "severity": 1, "message": "277", "line": 46, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 46, "endColumn": 13}, {"ruleId": "239", "severity": 1, "message": "278", "line": 52, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 52, "endColumn": 10}, {"ruleId": "239", "severity": 1, "message": "279", "line": 53, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 53, "endColumn": 11}, {"ruleId": "239", "severity": 1, "message": "280", "line": 58, "column": 19, "nodeType": "241", "messageId": "242", "endLine": 58, "endColumn": 35}, {"ruleId": "239", "severity": 1, "message": "281", "line": 64, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 64, "endColumn": 25}, {"ruleId": "239", "severity": 1, "message": "282", "line": 65, "column": 18, "nodeType": "241", "messageId": "242", "endLine": 65, "endColumn": 33}, {"ruleId": "239", "severity": 1, "message": "283", "line": 66, "column": 12, "nodeType": "241", "messageId": "242", "endLine": 66, "endColumn": 21}, {"ruleId": "239", "severity": 1, "message": "244", "line": 69, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 69, "endColumn": 31}, {"ruleId": "239", "severity": 1, "message": "268", "line": 70, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 70, "endColumn": 25}, {"ruleId": "239", "severity": 1, "message": "284", "line": 86, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 86, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "285", "line": 89, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 89, "endColumn": 26}, {"ruleId": "239", "severity": 1, "message": "286", "line": 116, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 116, "endColumn": 14}, {"ruleId": "239", "severity": 1, "message": "287", "line": 151, "column": 24, "nodeType": "241", "messageId": "242", "endLine": 151, "endColumn": 39}, {"ruleId": "239", "severity": 1, "message": "288", "line": 170, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 170, "endColumn": 22}, {"ruleId": "239", "severity": 1, "message": "289", "line": 170, "column": 24, "nodeType": "241", "messageId": "242", "endLine": 170, "endColumn": 39}, {"ruleId": "234", "severity": 1, "message": "290", "line": 292, "column": 15, "nodeType": "241", "endLine": 292, "endColumn": 22}, {"ruleId": "291", "severity": 1, "message": "292", "line": 81, "column": 1, "nodeType": "293", "endLine": 85, "endColumn": 3}, {"ruleId": "291", "severity": 1, "message": "292", "line": 151, "column": 1, "nodeType": "293", "endLine": 161, "endColumn": 3}, {"ruleId": "239", "severity": 1, "message": "278", "line": 22, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 22, "endColumn": 10}, {"ruleId": "239", "severity": 1, "message": "294", "line": 23, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 23, "endColumn": 17}, {"ruleId": "239", "severity": 1, "message": "295", "line": 24, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 24, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "296", "line": 25, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 25, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "297", "line": 26, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 26, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "298", "line": 31, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 31, "endColumn": 10}, {"ruleId": "239", "severity": 1, "message": "299", "line": 34, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 34, "endColumn": 31}, {"ruleId": "239", "severity": 1, "message": "281", "line": 37, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 37, "endColumn": 25}, {"ruleId": "239", "severity": 1, "message": "300", "line": 43, "column": 15, "nodeType": "241", "messageId": "242", "endLine": 43, "endColumn": 27}, {"ruleId": "239", "severity": 1, "message": "301", "line": 45, "column": 21, "nodeType": "241", "messageId": "242", "endLine": 45, "endColumn": 39}, {"ruleId": "239", "severity": 1, "message": "247", "line": 51, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 51, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "248", "line": 52, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 52, "endColumn": 7}, {"ruleId": "239", "severity": 1, "message": "272", "line": 63, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 63, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "273", "line": 64, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 64, "endColumn": 10}, {"ruleId": "239", "severity": 1, "message": "302", "line": 65, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 65, "endColumn": 13}, {"ruleId": "239", "severity": 1, "message": "303", "line": 66, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 66, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "304", "line": 67, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 67, "endColumn": 17}, {"ruleId": "239", "severity": 1, "message": "305", "line": 68, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 68, "endColumn": 18}, {"ruleId": "239", "severity": 1, "message": "306", "line": 69, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 69, "endColumn": 8}, {"ruleId": "234", "severity": 1, "message": "307", "line": 133, "column": 6, "nodeType": "236", "endLine": 133, "endColumn": 17, "suggestions": "308"}, {"ruleId": "239", "severity": 1, "message": "309", "line": 465, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 465, "endColumn": 27}, {"ruleId": "239", "severity": 1, "message": "310", "line": 29, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 29, "endColumn": 16}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAutoTradingStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["311"], ["312"], "@typescript-eslint/no-unused-vars", "'API_BASE_URL' is assigned a value but never used.", "Identifier", "unusedVar", "'TextField' is defined but never used.", "'AssessmentIcon' is defined but never used.", "'FileDownloadIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'AreaChart' is defined but never used.", "'Area' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["313"], "React Hook useEffect has an unnecessary dependency: 'selectedSessions'. Either exclude it or remove the dependency array. Outer scope values like 'selectedSessions' aren't valid dependencies because mutating them doesn't re-render the component.", ["314"], "'Paper' is defined but never used.", "'CircularProgress' is defined but never used.", "'SettingsIcon' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'StrategyWeight' is defined but never used.", "'StrategySignal' is defined but never used.", "'EnsemblePortfolioMetrics' is defined but never used.", "'StrategyCorrelation' is defined but never used.", "'RiskLimit' is defined but never used.", "'MLModelStatus' is defined but never used.", "'PerformanceDataPoint' is defined but never used.", "'RISK_STATUS_COLORS' is assigned a value but never used.", "'formatCurrency' is assigned a value but never used.", "'TrendingUpIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'ScatterChart' is defined but never used.", "'Scatter' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Divider' is defined but never used.", "'Collapse' is defined but never used.", "'TrendingDownIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorIcon' is defined but never used.", "'PortfolioSummary' is defined but never used.", "'BinanceWebSocketMessage' is defined but never used.", "'theme' is assigned a value but never used.", "'setTradeFilters' is assigned a value but never used.", "'priceUpdates' is assigned a value but never used.", "'setPriceUpdates' is assigned a value but never used.", "The ref value 'wsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'wsRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'LinearProgress' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'Tooltip' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'AccountBalanceIcon' is defined but never used.", "'RadarChart' is defined but never used.", "'PolarGrid' is defined but never used.", "'PolarAngleAxis' is defined but never used.", "'PolarRadiusAxis' is defined but never used.", "'Radar' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSessionData'. Either include it or remove the dependency array.", ["315"], "'strategyAnalysis' is assigned a value but never used.", "'entry' is assigned a value but never used.", {"desc": "316", "fix": "317"}, {"kind": "318", "justification": "319"}, {"desc": "320", "fix": "321"}, {"desc": "322", "fix": "323"}, {"desc": "324", "fix": "325"}, "Update the dependencies array to be: [fetchAutoTradingStatus]", {"range": "326", "text": "327"}, "directive", "", "Update the dependencies array to be: [timeFilter, statusFilter, fetchSessions]", {"range": "328", "text": "329"}, "Update the dependencies array to be: []", {"range": "330", "text": "331"}, "Update the dependencies array to be: [fetchSessionData, sessionId]", {"range": "332", "text": "333"}, [3632, 3634], "[fetchAutoTradingStatus]", [6808, 6834], "[timeFilter, statusFilter, fetchSessions]", [20248, 20266], "[]", [2841, 2852], "[fetchSessionData, sessionId]"]