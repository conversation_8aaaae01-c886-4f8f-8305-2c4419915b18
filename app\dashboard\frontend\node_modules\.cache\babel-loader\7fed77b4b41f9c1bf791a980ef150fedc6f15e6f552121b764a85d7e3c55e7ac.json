{"ast": null, "code": "// Corresponds to 10 frames at 60 Hz.\n// A few bytes payload overhead when lodash/debounce is ~3 kB and debounce ~300 B.\nexport default function debounce(func) {\n  let wait = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 166;\n  let timeout;\n  function debounced() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    const later = () => {\n      // @ts-ignore\n      func.apply(this, args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  }\n  debounced.clear = () => {\n    clearTimeout(timeout);\n  };\n  return debounced;\n}", "map": {"version": 3, "names": ["debounce", "func", "wait", "arguments", "length", "undefined", "timeout", "debounced", "_len", "args", "Array", "_key", "later", "apply", "clearTimeout", "setTimeout", "clear"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/utils/esm/debounce/debounce.js"], "sourcesContent": ["// Corresponds to 10 frames at 60 Hz.\n// A few bytes payload overhead when lodash/debounce is ~3 kB and debounce ~300 B.\nexport default function debounce(func, wait = 166) {\n  let timeout;\n  function debounced(...args) {\n    const later = () => {\n      // @ts-ignore\n      func.apply(this, args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  }\n  debounced.clear = () => {\n    clearTimeout(timeout);\n  };\n  return debounced;\n}"], "mappings": "AAAA;AACA;AACA,eAAe,SAASA,QAAQA,CAACC,IAAI,EAAc;EAAA,IAAZC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAC/C,IAAIG,OAAO;EACX,SAASC,SAASA,CAAA,EAAU;IAAA,SAAAC,IAAA,GAAAL,SAAA,CAAAC,MAAA,EAANK,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAR,SAAA,CAAAQ,IAAA;IAAA;IACxB,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClB;MACAX,IAAI,CAACY,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;IACxB,CAAC;IACDK,YAAY,CAACR,OAAO,CAAC;IACrBA,OAAO,GAAGS,UAAU,CAACH,KAAK,EAAEV,IAAI,CAAC;EACnC;EACAK,SAAS,CAACS,KAAK,GAAG,MAAM;IACtBF,YAAY,CAACR,OAAO,CAAC;EACvB,CAAC;EACD,OAAOC,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}