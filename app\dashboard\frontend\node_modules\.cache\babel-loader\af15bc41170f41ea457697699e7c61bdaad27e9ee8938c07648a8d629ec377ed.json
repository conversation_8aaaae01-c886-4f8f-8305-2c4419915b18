{"ast": null, "code": "export { default } from \"./useIsFocusVisible.js\";\nexport * from \"./useIsFocusVisible.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/utils/esm/useIsFocusVisible/index.js"], "sourcesContent": ["export { default } from \"./useIsFocusVisible.js\";\nexport * from \"./useIsFocusVisible.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}