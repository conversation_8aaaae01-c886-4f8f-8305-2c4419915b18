{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\nimport { StyleSheet } from '@emotion/sheet';\n\n// We might be able to remove this when this issue is fixed:\n// https://github.com/emotion-js/emotion/issues/2790\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst createEmotionCache = (options, CustomSheet) => {\n  const cache = createCache(options);\n\n  // Do the same as https://github.com/emotion-js/emotion/blob/main/packages/cache/src/index.js#L238-L245\n  cache.sheet = new CustomSheet({\n    key: cache.key,\n    nonce: cache.sheet.nonce,\n    container: cache.sheet.container,\n    speedy: cache.sheet.isSpeedy,\n    prepend: cache.sheet.prepend,\n    insertionPoint: cache.sheet.insertionPoint\n  });\n  return cache;\n};\nlet cache;\nif (typeof document === 'object') {\n  // Use `insertionPoint` over `prepend`(deprecated) because it can be controlled for GlobalStyles injection order\n  // For more information, see https://github.com/mui/material-ui/issues/44597\n  let insertionPoint = document.querySelector('[name=\"emotion-insertion-point\"]');\n  if (!insertionPoint) {\n    insertionPoint = document.createElement('meta');\n    insertionPoint.setAttribute('name', 'emotion-insertion-point');\n    insertionPoint.setAttribute('content', '');\n    const head = document.querySelector('head');\n    if (head) {\n      head.prepend(insertionPoint);\n    }\n  }\n  /**\n   * This is for client-side apps only.\n   * A custom sheet is required to make the GlobalStyles API injected above the insertion point.\n   * This is because the [sheet](https://github.com/emotion-js/emotion/blob/main/packages/react/src/global.js#L94-L99) does not consume the options.\n   */\n  class MyStyleSheet extends StyleSheet {\n    insert(rule, options) {\n      if (this.key && this.key.endsWith('global')) {\n        this.before = insertionPoint;\n      }\n      return super.insert(rule, options);\n    }\n  }\n  cache = createEmotionCache({\n    key: 'css',\n    insertionPoint\n  }, MyStyleSheet);\n}\nexport default function StyledEngineProvider(props) {\n  const {\n    injectFirst,\n    children\n  } = props;\n  return injectFirst && cache ? /*#__PURE__*/_jsx(CacheProvider, {\n    value: cache,\n    children: children\n  }) : children;\n}\nprocess.env.NODE_ENV !== \"production\" ? StyledEngineProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * By default, the styles are injected last in the <head> element of the page.\n   * As a result, they gain more specificity than any other style sheet.\n   * If you want to override MUI's styles, set this prop.\n   */\n  injectFirst: PropTypes.bool\n} : void 0;", "map": {"version": 3, "names": ["React", "PropTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createCache", "StyleSheet", "jsx", "_jsx", "createEmotionCache", "options", "CustomSheet", "cache", "sheet", "key", "nonce", "container", "speedy", "isSpeedy", "prepend", "insertionPoint", "document", "querySelector", "createElement", "setAttribute", "head", "MyStyleSheet", "insert", "rule", "endsWith", "before", "StyledEngineProvider", "props", "injectFirst", "children", "value", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/node_modules/@mui/styled-engine/StyledEngineProvider/StyledEngineProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\nimport { StyleSheet } from '@emotion/sheet';\n\n// We might be able to remove this when this issue is fixed:\n// https://github.com/emotion-js/emotion/issues/2790\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst createEmotionCache = (options, CustomSheet) => {\n  const cache = createCache(options);\n\n  // Do the same as https://github.com/emotion-js/emotion/blob/main/packages/cache/src/index.js#L238-L245\n  cache.sheet = new CustomSheet({\n    key: cache.key,\n    nonce: cache.sheet.nonce,\n    container: cache.sheet.container,\n    speedy: cache.sheet.isSpeedy,\n    prepend: cache.sheet.prepend,\n    insertionPoint: cache.sheet.insertionPoint\n  });\n  return cache;\n};\nlet cache;\nif (typeof document === 'object') {\n  // Use `insertionPoint` over `prepend`(deprecated) because it can be controlled for GlobalStyles injection order\n  // For more information, see https://github.com/mui/material-ui/issues/44597\n  let insertionPoint = document.querySelector('[name=\"emotion-insertion-point\"]');\n  if (!insertionPoint) {\n    insertionPoint = document.createElement('meta');\n    insertionPoint.setAttribute('name', 'emotion-insertion-point');\n    insertionPoint.setAttribute('content', '');\n    const head = document.querySelector('head');\n    if (head) {\n      head.prepend(insertionPoint);\n    }\n  }\n  /**\n   * This is for client-side apps only.\n   * A custom sheet is required to make the GlobalStyles API injected above the insertion point.\n   * This is because the [sheet](https://github.com/emotion-js/emotion/blob/main/packages/react/src/global.js#L94-L99) does not consume the options.\n   */\n  class MyStyleSheet extends StyleSheet {\n    insert(rule, options) {\n      if (this.key && this.key.endsWith('global')) {\n        this.before = insertionPoint;\n      }\n      return super.insert(rule, options);\n    }\n  }\n  cache = createEmotionCache({\n    key: 'css',\n    insertionPoint\n  }, MyStyleSheet);\n}\nexport default function StyledEngineProvider(props) {\n  const {\n    injectFirst,\n    children\n  } = props;\n  return injectFirst && cache ? /*#__PURE__*/_jsx(CacheProvider, {\n    value: cache,\n    children: children\n  }) : children;\n}\nprocess.env.NODE_ENV !== \"production\" ? StyledEngineProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * By default, the styles are injected last in the <head> element of the page.\n   * As a result, they gain more specificity than any other style sheet.\n   * If you want to override MUI's styles, set this prop.\n   */\n  injectFirst: PropTypes.bool\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,SAASC,UAAU,QAAQ,gBAAgB;;AAE3C;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,WAAW,KAAK;EACnD,MAAMC,KAAK,GAAGP,WAAW,CAACK,OAAO,CAAC;;EAElC;EACAE,KAAK,CAACC,KAAK,GAAG,IAAIF,WAAW,CAAC;IAC5BG,GAAG,EAAEF,KAAK,CAACE,GAAG;IACdC,KAAK,EAAEH,KAAK,CAACC,KAAK,CAACE,KAAK;IACxBC,SAAS,EAAEJ,KAAK,CAACC,KAAK,CAACG,SAAS;IAChCC,MAAM,EAAEL,KAAK,CAACC,KAAK,CAACK,QAAQ;IAC5BC,OAAO,EAAEP,KAAK,CAACC,KAAK,CAACM,OAAO;IAC5BC,cAAc,EAAER,KAAK,CAACC,KAAK,CAACO;EAC9B,CAAC,CAAC;EACF,OAAOR,KAAK;AACd,CAAC;AACD,IAAIA,KAAK;AACT,IAAI,OAAOS,QAAQ,KAAK,QAAQ,EAAE;EAChC;EACA;EACA,IAAID,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,kCAAkC,CAAC;EAC/E,IAAI,CAACF,cAAc,EAAE;IACnBA,cAAc,GAAGC,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;IAC/CH,cAAc,CAACI,YAAY,CAAC,MAAM,EAAE,yBAAyB,CAAC;IAC9DJ,cAAc,CAACI,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC;IAC1C,MAAMC,IAAI,GAAGJ,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3C,IAAIG,IAAI,EAAE;MACRA,IAAI,CAACN,OAAO,CAACC,cAAc,CAAC;IAC9B;EACF;EACA;AACF;AACA;AACA;AACA;EACE,MAAMM,YAAY,SAASpB,UAAU,CAAC;IACpCqB,MAAMA,CAACC,IAAI,EAAElB,OAAO,EAAE;MACpB,IAAI,IAAI,CAACI,GAAG,IAAI,IAAI,CAACA,GAAG,CAACe,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC3C,IAAI,CAACC,MAAM,GAAGV,cAAc;MAC9B;MACA,OAAO,KAAK,CAACO,MAAM,CAACC,IAAI,EAAElB,OAAO,CAAC;IACpC;EACF;EACAE,KAAK,GAAGH,kBAAkB,CAAC;IACzBK,GAAG,EAAE,KAAK;IACVM;EACF,CAAC,EAAEM,YAAY,CAAC;AAClB;AACA,eAAe,SAASK,oBAAoBA,CAACC,KAAK,EAAE;EAClD,MAAM;IACJC,WAAW;IACXC;EACF,CAAC,GAAGF,KAAK;EACT,OAAOC,WAAW,IAAIrB,KAAK,GAAG,aAAaJ,IAAI,CAACJ,aAAa,EAAE;IAC7D+B,KAAK,EAAEvB,KAAK;IACZsB,QAAQ,EAAEA;EACZ,CAAC,CAAC,GAAGA,QAAQ;AACf;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,oBAAoB,CAACQ,SAAS,GAAG;EACvE;AACF;AACA;EACEL,QAAQ,EAAE/B,SAAS,CAACqC,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEP,WAAW,EAAE9B,SAAS,CAACsC;AACzB,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}