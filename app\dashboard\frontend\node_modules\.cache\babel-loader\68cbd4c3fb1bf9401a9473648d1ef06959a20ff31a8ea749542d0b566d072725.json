{"ast": null, "code": "export const getStatusColor=status=>{const colors={PENDING_ENTRY:'default',ENTRY_FILLED:'primary',SLTP_PLACED:'info',CLOSED_SL:'warning',CLOSED_TP:'success',CLOSED_MANUAL:'warning',ERROR:'error'};return colors[status]||'default';};export const formatPrice=price=>{if(price===null||price===undefined)return'N/A';return price.toFixed(Math.max(2,(price.toString().split('.')[1]||'').length));};export const formatDateTime=dateString=>{return new Date(dateString).toLocaleString();};", "map": {"version": 3, "names": ["getStatusColor", "status", "colors", "PENDING_ENTRY", "ENTRY_FILLED", "SLTP_PLACED", "CLOSED_SL", "CLOSED_TP", "CLOSED_MANUAL", "ERROR", "formatPrice", "price", "undefined", "toFixed", "Math", "max", "toString", "split", "length", "formatDateTime", "dateString", "Date", "toLocaleString"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/utils/formatters.ts"], "sourcesContent": ["type StatusColor = 'default' | 'primary' | 'info' | 'error' | 'success' | 'warning';\n\nexport const getStatusColor = (status: string): StatusColor => {\n  const colors: { [key: string]: StatusColor } = {\n    PENDING_ENTRY: 'default',\n    ENTRY_FILLED: 'primary',\n    SLTP_PLACED: 'info',\n    CLOSED_SL: 'warning',\n    CLOSED_TP: 'success',\n    CLOSED_MANUAL: 'warning',\n    ERROR: 'error',\n  };\n  return colors[status] || 'default';\n};\n\nexport const formatPrice = (price: number | null | undefined): string => {\n  if (price === null || price === undefined) return 'N/A';\n  return price.toFixed(Math.max(2, (price.toString().split('.')[1] || '').length));\n};\n\nexport const formatDateTime = (dateString: string): string => {\n  return new Date(dateString).toLocaleString();\n}; "], "mappings": "AAEA,MAAO,MAAM,CAAAA,cAAc,CAAIC,MAAc,EAAkB,CAC7D,KAAM,CAAAC,MAAsC,CAAG,CAC7CC,aAAa,CAAE,SAAS,CACxBC,YAAY,CAAE,SAAS,CACvBC,WAAW,CAAE,MAAM,CACnBC,SAAS,CAAE,SAAS,CACpBC,SAAS,CAAE,SAAS,CACpBC,aAAa,CAAE,SAAS,CACxBC,KAAK,CAAE,OACT,CAAC,CACD,MAAO,CAAAP,MAAM,CAACD,MAAM,CAAC,EAAI,SAAS,CACpC,CAAC,CAED,MAAO,MAAM,CAAAS,WAAW,CAAIC,KAAgC,EAAa,CACvE,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,CAAE,MAAO,KAAK,CACvD,MAAO,CAAAD,KAAK,CAACE,OAAO,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAI,EAAE,EAAEC,MAAM,CAAC,CAAC,CAClF,CAAC,CAED,MAAO,MAAM,CAAAC,cAAc,CAAIC,UAAkB,EAAa,CAC5D,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,CAAC,CAC9C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}